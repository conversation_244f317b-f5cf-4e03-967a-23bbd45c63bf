import { useState, useEffect } from 'react';

const DateExclusionPage = () => {
  const [exclusions, setExclusions] = useState([]);
  const [newExclusion, setNewExclusion] = useState({
    date: '',
    raison: '',
    type: 'ferie'
  });
  const [showForm, setShowForm] = useState(false);

  // Jours fériés français par défaut
  const defaultHolidays = [
    { date: '2024-01-01', raison: 'Jour de l\'An', type: 'ferie' },
    { date: '2024-05-01', raison: 'Fête du Travail', type: 'ferie' },
    { date: '2024-05-08', raison: 'Fête de la Victoire', type: 'ferie' },
    { date: '2024-07-14', raison: 'Fête Nationale', type: 'ferie' },
    { date: '2024-08-15', raison: 'Assomption', type: 'ferie' },
    { date: '2024-11-01', raison: 'Toussaint', type: 'ferie' },
    { date: '2024-11-11', raison: 'Armistice', type: 'ferie' },
    { date: '2024-12-25', raison: 'Noël', type: 'ferie' }
  ];

  useEffect(() => {
    // Charger les exclusions depuis le localStorage ou utiliser les défauts
    const savedExclusions = localStorage.getItem('dateExclusions');
    if (savedExclusions) {
      setExclusions(JSON.parse(savedExclusions));
    } else {
      setExclusions(defaultHolidays);
      localStorage.setItem('dateExclusions', JSON.stringify(defaultHolidays));
    }
  }, []);

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!newExclusion.date || !newExclusion.raison) {
      alert('Veuillez remplir tous les champs');
      return;
    }

    const exclusion = {
      id: Date.now(),
      ...newExclusion,
      dateAjout: new Date().toISOString()
    };

    const updatedExclusions = [...exclusions, exclusion];
    setExclusions(updatedExclusions);
    localStorage.setItem('dateExclusions', JSON.stringify(updatedExclusions));

    setNewExclusion({ date: '', raison: '', type: 'ferie' });
    setShowForm(false);
  };

  const handleDelete = (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette exclusion ?')) {
      const updatedExclusions = exclusions.filter(ex => ex.id !== id);
      setExclusions(updatedExclusions);
      localStorage.setItem('dateExclusions', JSON.stringify(updatedExclusions));
    }
  };

  const resetToDefaults = () => {
    if (window.confirm('Réinitialiser aux jours fériés par défaut ? Cela supprimera toutes vos exclusions personnalisées.')) {
      setExclusions(defaultHolidays);
      localStorage.setItem('dateExclusions', JSON.stringify(defaultHolidays));
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'ferie': return '🎉';
      case 'conge': return '🏖️';
      case 'formation': return '📚';
      case 'autre': return '📝';
      default: return '📅';
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'ferie': return 'Jour férié';
      case 'conge': return 'Congé collectif';
      case 'formation': return 'Formation';
      case 'autre': return 'Autre';
      default: return 'Non défini';
    }
  };

  return (
    <div className="date-exclusion-page">
      <div className="page-header">
        <div className="header-content">
          <h2>📅 Gestion des Dates d'Exclusion</h2>
          <p>Gérez les jours fériés et autres dates à exclure des plannings</p>
        </div>
        <div className="header-actions">
          <button
            onClick={() => setShowForm(!showForm)}
            className={`btn ${showForm ? 'btn-secondary' : 'btn-primary'}`}
          >
            {showForm ? '❌ Annuler' : '➕ Ajouter une exclusion'}
          </button>
          <button
            onClick={resetToDefaults}
            className="btn btn-warning"
          >
            🔄 Réinitialiser
          </button>
        </div>
      </div>

      <div className="page-content">
        {/* Formulaire d'ajout */}
        {showForm && (
          <div className="form-section">
            <div className="section-card">
              <h3>➕ Nouvelle exclusion</h3>
              <form onSubmit={handleSubmit} className="exclusion-form">
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="date">Date :</label>
                    <input
                      type="date"
                      id="date"
                      value={newExclusion.date}
                      onChange={(e) => setNewExclusion(prev => ({ ...prev, date: e.target.value }))}
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="type">Type :</label>
                    <select
                      id="type"
                      value={newExclusion.type}
                      onChange={(e) => setNewExclusion(prev => ({ ...prev, type: e.target.value }))}
                    >
                      <option value="ferie">Jour férié</option>
                      <option value="conge">Congé collectif</option>
                      <option value="formation">Formation</option>
                      <option value="autre">Autre</option>
                    </select>
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="raison">Raison :</label>
                  <input
                    type="text"
                    id="raison"
                    value={newExclusion.raison}
                    onChange={(e) => setNewExclusion(prev => ({ ...prev, raison: e.target.value }))}
                    placeholder="Ex: Noël, Formation équipe, etc."
                    required
                  />
                </div>

                <div className="form-actions">
                  <button type="submit" className="btn btn-primary">
                    ✅ Ajouter l'exclusion
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Liste des exclusions */}
        <div className="list-section">
          <div className="section-card">
            <div className="card-header">
              <h3>📋 Dates d'exclusion ({exclusions.length})</h3>
            </div>
            
            {exclusions.length === 0 ? (
              <div className="empty-state">
                <p>Aucune date d'exclusion définie</p>
                <p>Ajoutez des jours fériés ou autres dates à exclure des plannings</p>
              </div>
            ) : (
              <div className="exclusions-list">
                {exclusions
                  .sort((a, b) => new Date(a.date) - new Date(b.date))
                  .map(exclusion => (
                    <div key={exclusion.id || exclusion.date} className="exclusion-item">
                      <div className="exclusion-icon">
                        {getTypeIcon(exclusion.type)}
                      </div>
                      <div className="exclusion-content">
                        <h4>{exclusion.raison}</h4>
                        <p className="exclusion-date">{formatDate(exclusion.date)}</p>
                        <span className={`type-badge ${exclusion.type}`}>
                          {getTypeLabel(exclusion.type)}
                        </span>
                      </div>
                      <div className="exclusion-actions">
                        <button
                          onClick={() => handleDelete(exclusion.id || exclusion.date)}
                          className="btn btn-danger btn-small"
                          title="Supprimer cette exclusion"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DateExclusionPage;
