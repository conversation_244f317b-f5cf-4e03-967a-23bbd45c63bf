import { useState, useEffect } from 'react';
import { dateExclusionService } from '../services/api';

const DateExclusionPage = () => {
  const [exclusions, setExclusions] = useState([]);
  const [newExclusion, setNewExclusion] = useState({
    date: '',
    raison: '',
    type: 'FERIE'
  });
  const [showForm, setShowForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    loadExclusions();
  }, []);

  const loadExclusions = async () => {
    try {
      setLoading(true);
      const data = await dateExclusionService.getAll();
      setExclusions(data);
    } catch (err) {
      setError('Erreur lors du chargement des exclusions');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!newExclusion.date || !newExclusion.raison) {
      setError('Veuillez remplir tous les champs');
      return;
    }

    try {
      setLoading(true);
      setError('');
      await dateExclusionService.create(newExclusion);
      await loadExclusions();
      setNewExclusion({ date: '', raison: '', type: 'FERIE' });
      setShowForm(false);
    } catch (err) {
      setError(err.response?.data?.error || 'Erreur lors de l\'ajout');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette exclusion ?')) {
      try {
        setLoading(true);
        await dateExclusionService.delete(id);
        await loadExclusions();
      } catch (err) {
        setError(err.response?.data?.error || 'Erreur lors de la suppression');
      } finally {
        setLoading(false);
      }
    }
  };

  const resetToDefaults = async () => {
    if (window.confirm('Réinitialiser aux jours fériés par défaut ? Cela supprimera toutes vos exclusions personnalisées.')) {
      try {
        setLoading(true);
        await dateExclusionService.reset();
        await loadExclusions();
      } catch (err) {
        setError(err.response?.data?.error || 'Erreur lors de la réinitialisation');
      } finally {
        setLoading(false);
      }
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getTypeIcon = (type) => {
    switch (type) {
      case 'FERIE': return '🎉';
      case 'CONGE': return '🏖️';
      case 'FORMATION': return '📚';
      case 'AUTRE': return '📝';
      default: return '📅';
    }
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'FERIE': return 'Jour férié';
      case 'CONGE': return 'Congé collectif';
      case 'FORMATION': return 'Formation';
      case 'AUTRE': return 'Autre';
      default: return 'Non défini';
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-red-600 to-sky-600 bg-clip-text text-transparent flex items-center gap-3">
            📅 Gestion des Dates d'Exclusion
          </h2>
          <p className="text-gray-600 mt-2">Gérez les jours fériés et autres dates à exclure des plannings</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => setShowForm(!showForm)}
            className={`px-6 py-3 rounded-xl transition-all duration-300 flex items-center gap-2 font-semibold ${
              showForm
                ? 'bg-gray-500 text-white hover:bg-gray-600 hover:shadow-lg'
                : 'bg-gradient-to-r from-red-500 to-sky-500 text-white hover:from-red-600 hover:to-sky-600 hover:shadow-lg hover:-translate-y-0.5'
            }`}
          >
            {showForm ? '❌ Annuler' : '➕ Ajouter une exclusion'}
          </button>
          <button
            onClick={resetToDefaults}
            className="px-6 py-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-xl hover:from-yellow-600 hover:to-orange-600 transition-all duration-300 flex items-center gap-2 font-semibold hover:shadow-lg"
          >
            🔄 Réinitialiser
          </button>
        </div>
      </div>

      <div className="page-content">
        {error && (
          <div className="error-message">
            ❌ {error}
          </div>
        )}

        {/* Formulaire d'ajout */}
        {showForm && (
          <div className="form-section">
            <div className="section-card">
              <h3>➕ Nouvelle exclusion</h3>
              <form onSubmit={handleSubmit} className="exclusion-form">
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="date">Date :</label>
                    <input
                      type="date"
                      id="date"
                      value={newExclusion.date}
                      onChange={(e) => setNewExclusion(prev => ({ ...prev, date: e.target.value }))}
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="type">Type :</label>
                    <select
                      id="type"
                      value={newExclusion.type}
                      onChange={(e) => setNewExclusion(prev => ({ ...prev, type: e.target.value }))}
                    >
                      <option value="FERIE">Jour férié</option>
                      <option value="CONGE">Congé collectif</option>
                      <option value="FORMATION">Formation</option>
                      <option value="AUTRE">Autre</option>
                    </select>
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="raison">Raison :</label>
                  <input
                    type="text"
                    id="raison"
                    value={newExclusion.raison}
                    onChange={(e) => setNewExclusion(prev => ({ ...prev, raison: e.target.value }))}
                    placeholder="Ex: Noël, Formation équipe, etc."
                    required
                  />
                </div>

                <div className="form-actions">
                  <button type="submit" className="btn btn-primary" disabled={loading}>
                    {loading ? '⏳ Ajout...' : '✅ Ajouter l\'exclusion'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Liste des exclusions */}
        <div className="list-section">
          <div className="section-card">
            <div className="card-header">
              <h3>📋 Dates d'exclusion ({exclusions.length})</h3>
            </div>

            {exclusions.length === 0 ? (
              <div className="empty-state">
                <p>Aucune date d'exclusion définie</p>
                <p>Ajoutez des jours fériés ou autres dates à exclure des plannings</p>
              </div>
            ) : (
              <div className="exclusions-list">
                {exclusions
                  .sort((a, b) => new Date(a.date) - new Date(b.date))
                  .map(exclusion => (
                    <div key={exclusion.id || exclusion.date} className="exclusion-item">
                      <div className="exclusion-icon">
                        {getTypeIcon(exclusion.type)}
                      </div>
                      <div className="exclusion-content">
                        <h4>{exclusion.raison}</h4>
                        <p className="exclusion-date">{formatDate(exclusion.date)}</p>
                        <span className={`type-badge ${exclusion.type}`}>
                          {getTypeLabel(exclusion.type)}
                        </span>
                      </div>
                      <div className="exclusion-actions">
                        <button
                          onClick={() => handleDelete(exclusion.id || exclusion.date)}
                          className="btn btn-danger btn-small"
                          title="Supprimer cette exclusion"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DateExclusionPage;
