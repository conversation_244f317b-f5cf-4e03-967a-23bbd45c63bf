import { useState, useEffect } from 'react';
import { medecinService } from '../services/api';

const Sidebar = ({ currentPage, onNavigate }) => {
  const [stats, setStats] = useState({
    totalMedecins: 0,
    assistants: 0,
    residants: 0
  });

  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: '📊',
      description: 'Vue d\'ensemble'
    },
    {
      id: 'medecins',
      label: 'Médecins',
      icon: '👥',
      description: 'Gestion des médecins'
    },
    {
      id: 'garde-config',
      label: 'Configuration',
      icon: '⚙️',
      description: 'Configuration des gardes'
    },
    {
      id: 'date-exclusion',
      label: 'Dates d\'exclusion',
      icon: '📅',
      description: 'Jours fériés et exclusions'
    }
  ];

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const medecins = await medecinService.getAll();
      setStats({
        totalMedecins: medecins.length,
        assistants: medecins.filter(m => m.type === 'Assistant').length,
        residants: medecins.filter(m => m.type === 'Residant').length
      });
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    }
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <div className="logo">
          <span className="logo-icon">🏥</span>
          <span className="logo-text">GardeMed</span>
        </div>
      </div>

      <nav className="sidebar-nav">
        {menuItems.map(item => (
          <button
            key={item.id}
            onClick={() => onNavigate(item.id)}
            className={`nav-item ${currentPage === item.id ? 'active' : ''}`}
          >
            <span className="nav-icon">{item.icon}</span>
            <div className="nav-content">
              <span className="nav-label">{item.label}</span>
              <span className="nav-description">{item.description}</span>
            </div>
          </button>
        ))}
      </nav>

      <div className="sidebar-stats">
        <h3>📈 Statistiques</h3>
        <div className="stat-item">
          <span className="stat-label">Total médecins</span>
          <span className="stat-value">{stats.totalMedecins}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Assistants</span>
          <span className="stat-value">{stats.assistants}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Résidants</span>
          <span className="stat-value">{stats.residants}</span>
        </div>
      </div>

      <div className="sidebar-footer">
        <div className="version-info">
          <span>Version 1.0</span>
          <span>© 2024 GardeMed</span>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
