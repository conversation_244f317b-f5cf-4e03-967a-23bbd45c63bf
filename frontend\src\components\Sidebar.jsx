import { useState, useEffect } from 'react';
import { medecinService } from '../services/api';

const Sidebar = ({ currentPage, onNavigate }) => {
  const [stats, setStats] = useState({
    totalMedecins: 0,
    assistants: 0,
    residants: 0
  });

  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: '📊',
      description: 'Vue d\'ensemble'
    },
    {
      id: 'medecins',
      label: 'Médecins',
      icon: '👥',
      description: 'Gestion des médecins'
    },
    {
      id: 'garde-config',
      label: 'Configuration',
      icon: '⚙️',
      description: 'Configuration des gardes'
    },
    {
      id: 'date-exclusion',
      label: 'Dates d\'exclusion',
      icon: '📅',
      description: 'Jours fériés et exclusions'
    }
  ];

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const medecins = await medecinService.getAll();
      setStats({
        totalMedecins: medecins.length,
        assistants: medecins.filter(m => m.type === 'Assistant').length,
        residants: medecins.filter(m => m.type === 'Residant').length
      });
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    }
  };

  return (
    <div className="fixed left-0 top-0 h-full w-64 bg-gray-900 text-white flex flex-col">
      <div className="p-6 border-b border-gray-700">
        <div className="flex items-center gap-3">
          <span className="text-2xl">🏥</span>
          <span className="text-xl font-bold">GardeMed</span>
        </div>
      </div>

      <nav className="flex-1 py-4">
        {menuItems.map(item => (
          <button
            key={item.id}
            onClick={() => onNavigate(item.id)}
            className={`w-full flex items-center gap-4 px-6 py-3 text-left transition-colors ${
              currentPage === item.id
                ? 'bg-blue-600 text-white'
                : 'text-gray-300 hover:bg-gray-800 hover:text-white'
            }`}
          >
            <span className="text-xl w-6 text-center">{item.icon}</span>
            <div>
              <div className="font-medium text-sm">{item.label}</div>
              <div className="text-xs opacity-70">{item.description}</div>
            </div>
          </button>
        ))}
      </nav>

      <div className="p-6 border-t border-gray-700">
        <h3 className="text-sm font-medium mb-4 text-gray-400">📈 Statistiques</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-400">Total médecins</span>
            <span className="text-blue-400 font-medium">{stats.totalMedecins}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Assistants</span>
            <span className="text-green-400 font-medium">{stats.assistants}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-400">Résidants</span>
            <span className="text-yellow-400 font-medium">{stats.residants}</span>
          </div>
        </div>
      </div>

      <div className="p-6 border-t border-gray-700">
        <div className="text-xs text-gray-500 space-y-1">
          <div>Version 1.0</div>
          <div>© 2024 GardeMed</div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
