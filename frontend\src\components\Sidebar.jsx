import { useState, useEffect } from 'react';
import { medecinService } from '../services/api';

const Sidebar = ({ currentPage, onNavigate }) => {
  const [stats, setStats] = useState({
    totalMedecins: 0,
    assistants: 0,
    residants: 0
  });

  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: '📊',
      description: 'Vue d\'ensemble'
    },
    {
      id: 'medecins',
      label: 'Médecins',
      icon: '👥',
      description: 'Gestion des médecins'
    },
    {
      id: 'garde-config',
      label: 'Configuration',
      icon: '⚙️',
      description: 'Configuration des gardes'
    },
    {
      id: 'date-exclusion',
      label: 'Dates d\'exclusion',
      icon: '📅',
      description: 'Jours fériés et exclusions'
    }
  ];

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const medecins = await medecinService.getAll();
      setStats({
        totalMedecins: medecins.length,
        assistants: medecins.filter(m => m.type === 'Assistant').length,
        residants: medecins.filter(m => m.type === 'Residant').length
      });
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    }
  };

  return (
    <div className="fixed left-0 top-0 h-full w-72 bg-white border-r border-gray-200 flex flex-col shadow-lg">
      {/* Header avec logo */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-sky-500 rounded-xl flex items-center justify-center">
            <span className="text-white text-xl font-bold">🏥</span>
          </div>
          <div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-red-600 to-sky-600 bg-clip-text text-transparent">
              GardeMed
            </h1>
            <p className="text-xs text-gray-500">Gestion des gardes</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 py-6 px-4">
        <div className="space-y-2">
          {menuItems.map(item => (
            <button
              key={item.id}
              onClick={() => onNavigate(item.id)}
              className={`w-full flex items-center gap-4 px-4 py-3 rounded-xl text-left transition-all duration-200 group ${
                currentPage === item.id
                  ? 'bg-gradient-to-r from-red-50 to-sky-50 border border-red-200 text-red-700 shadow-sm'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className={`w-10 h-10 rounded-lg flex items-center justify-center transition-colors ${
                currentPage === item.id
                  ? 'bg-gradient-to-br from-red-500 to-sky-500 text-white'
                  : 'bg-gray-100 text-gray-600 group-hover:bg-gray-200'
              }`}>
                <span className="text-lg">{item.icon}</span>
              </div>
              <div className="flex-1">
                <div className="font-semibold text-sm">{item.label}</div>
                <div className="text-xs opacity-70">{item.description}</div>
              </div>
              {currentPage === item.id && (
                <div className="w-2 h-2 bg-gradient-to-br from-red-500 to-sky-500 rounded-full"></div>
              )}
            </button>
          ))}
        </div>
      </nav>

      {/* Statistiques */}
      <div className="p-6 border-t border-gray-100">
        <h3 className="text-sm font-semibold mb-4 text-gray-700 flex items-center gap-2">
          📈 Statistiques
        </h3>
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-gradient-to-br from-red-50 to-red-100 p-3 rounded-lg">
            <div className="text-lg font-bold text-red-600">{stats.totalMedecins}</div>
            <div className="text-xs text-red-700">Médecins</div>
          </div>
          <div className="bg-gradient-to-br from-sky-50 to-sky-100 p-3 rounded-lg">
            <div className="text-lg font-bold text-sky-600">{stats.assistants}</div>
            <div className="text-xs text-sky-700">Assistants</div>
          </div>
          <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-3 rounded-lg">
            <div className="text-lg font-bold text-purple-600">{stats.residants}</div>
            <div className="text-xs text-purple-700">Résidants</div>
          </div>
          <div className="bg-gradient-to-br from-green-50 to-green-100 p-3 rounded-lg">
            <div className="text-lg font-bold text-green-600">12</div>
            <div className="text-xs text-green-700">Gardes</div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-6 border-t border-gray-100">
        <div className="text-center">
          <div className="text-xs text-gray-400 space-y-1">
            <div className="font-medium">Version 1.0</div>
            <div>© 2024 GardeMed Pro</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
