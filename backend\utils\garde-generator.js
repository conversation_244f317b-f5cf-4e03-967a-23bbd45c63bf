class GardeGenerator {
  constructor() {
    this.jours = ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'];
    this.mois = [
      'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
      'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
    ];
  }

  // Génère toutes les dates d'un mois donné
  genererDatesMois(mois, annee) {
    const moisIndex = this.mois.indexOf(mois.toLowerCase());
    if (moisIndex === -1) {
      throw new Error('Mois invalide');
    }

    const dates = [];
    const nombreJours = new Date(annee, moisIndex + 1, 0).getDate();

    for (let jour = 1; jour <= nombreJours; jour++) {
      const date = new Date(annee, moisIndex, jour);
      dates.push({
        date: `${jour.toString().padStart(2, '0')}/${(moisIndex + 1).toString().padStart(2, '0')}/${annee}`,
        jour: this.jours[date.getDay()],
        timestamp: date.getTime()
      });
    }

    return dates;
  }

  // Génère la répartition des gardes
  genererGardes(medecins, configurations, mois, annee) {
    const dates = this.genererDatesMois(mois, annee);
    const assistants = medecins.filter(m => m.type === 'Assistant');
    const residants = medecins.filter(m => m.type === 'Résidant');

    // Créer les listes de gardes à attribuer
    const gardesAssistants = this.creerListeGardes(assistants, configurations);
    const gardesResidants = this.creerListeGardes(residants, configurations);

    // Mélanger les listes pour une répartition aléatoire
    this.melangerArray(gardesAssistants);
    this.melangerArray(gardesResidants);

    const gardes = [];
    const compteurSemaine = new Map(); // Pour suivre les gardes par semaine

    dates.forEach((dateInfo, index) => {
      const semaine = this.obtenirSemaine(dateInfo.timestamp);

      // Attribuer assistant
      const assistantId = this.attribuerMedecin(
        gardesAssistants,
        index,
        semaine,
        compteurSemaine,
        'assistant'
      );

      // Attribuer résidant
      const residantId = this.attribuerMedecin(
        gardesResidants,
        index,
        semaine,
        compteurSemaine,
        'residant'
      );

      gardes.push({
        date: dateInfo.date,
        jour: dateInfo.jour,
        medecin_assistant_id: assistantId,
        medecin_residant_id: residantId,
        mois: mois,
        annee: annee
      });
    });

    return gardes;
  }

  creerListeGardes(medecins, configurations) {
    const liste = [];

    medecins.forEach(medecin => {
      const config = configurations.find(c => c.medecinId === medecin.id);
      const nombreGardes = config ? config.nombreGardes : 0;

      for (let i = 0; i < nombreGardes; i++) {
        liste.push(medecin.id);
      }
    });

    return liste;
  }

  attribuerMedecin(listeGardes, indexDate, semaine, compteurSemaine, type) {
    if (listeGardes.length === 0) return null;

    // Essayer de trouver un médecin qui n'a pas déjà 3 gardes cette semaine
    for (let i = 0; i < listeGardes.length; i++) {
      const medecinId = listeGardes[i];
      const cleSemaine = `${semaine}-${medecinId}-${type}`;
      const gardesSemaine = compteurSemaine.get(cleSemaine) || 0;

      if (gardesSemaine < 3) {
        // Attribuer cette garde
        listeGardes.splice(i, 1);
        compteurSemaine.set(cleSemaine, gardesSemaine + 1);
        return medecinId;
      }
    }

    // Si aucun médecin disponible avec moins de 3 gardes, prendre le premier
    if (listeGardes.length > 0) {
      const medecinId = listeGardes.shift();
      const cleSemaine = `${semaine}-${medecinId}-${type}`;
      const gardesSemaine = compteurSemaine.get(cleSemaine) || 0;
      compteurSemaine.set(cleSemaine, gardesSemaine + 1);
      return medecinId;
    }

    return null;
  }

  obtenirSemaine(timestamp) {
    const date = new Date(timestamp);
    const premierJanvier = new Date(date.getFullYear(), 0, 1);
    const joursEcoules = Math.floor((date - premierJanvier) / (24 * 60 * 60 * 1000));
    return Math.ceil((joursEcoules + premierJanvier.getDay() + 1) / 7);
  }

  melangerArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  }

  // Supprimer une date de la liste des gardes
  supprimerDate(gardes, dateASupprimer) {
    return gardes.filter(garde => garde.date !== dateASupprimer);
  }
}

module.exports = GardeGenerator;
