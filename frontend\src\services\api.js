import axios from 'axios';

const API_BASE_URL = 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Services pour les médecins
export const medecinService = {
  // Récupérer tous les médecins
  getAll: async () => {
    const response = await api.get('/medecins');
    return response.data;
  },

  // Ajouter un médecin
  create: async (medecin) => {
    const response = await api.post('/medecins', medecin);
    return response.data;
  },

  // Supprimer un médecin
  delete: async (id) => {
    const response = await api.delete(`/medecins/${id}`);
    return response.data;
  },
};

// Services pour les gardes
export const gardeService = {
  // Sauvegarder la configuration des gardes
  saveConfiguration: async (configurations, mois, annee) => {
    const response = await api.post('/gardes/configuration', {
      configurations,
      mois,
      annee,
    });
    return response.data;
  },

  // Générer les gardes
  generate: async (mois, annee) => {
    const response = await api.post('/gardes/generer', {
      mois,
      annee,
    });
    return response.data;
  },

  // Récupérer les gardes d'un mois
  getByMonth: async (mois, annee) => {
    const response = await api.get(`/gardes/${mois}/${annee}`);
    return response.data;
  },

  // Sauvegarder un planning complet
  saveGardes: async (gardes) => {
    const response = await api.post('/gardes/save', { gardes });
    return response.data;
  },

  // Supprimer une garde
  delete: async (id) => {
    const response = await api.delete(`/gardes/${id}`);
    return response.data;
  },

  // Exporter en Word
  exportToWord: async (mois, annee) => {
    const response = await api.get(`/gardes/export/${mois}/${annee}`, {
      responseType: 'blob',
    });
    return response.data;
  },
};

export default api;
