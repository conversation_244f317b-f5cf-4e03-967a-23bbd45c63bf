const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { Document, Packer, Table, TableRow, TableCell, Paragraph, TextRun, WidthType } = require('docx');
const GardeGenerator = require('../utils/garde-generator');

const router = express.Router();
const prisma = new PrismaClient();
const gardeGenerator = new GardeGenerator();

// POST /api/gardes/configuration - Sauvegarder la configuration des gardes
router.post('/configuration', async (req, res) => {
  try {
    const { configurations, mois, annee } = req.body;

    // Validation
    if (!configurations || !Array.isArray(configurations) || !mois || !annee) {
      return res.status(400).json({ error: 'Données invalides' });
    }

    // Supprimer l'ancienne configuration pour ce mois
    await prisma.gardeConfig.deleteMany({
      where: {
        mois: mois,
        annee: parseInt(annee)
      }
    });

    // Créer la nouvelle configuration
    const configData = configurations.map(config => ({
      medecinId: parseInt(config.medecinId),
      nombreGardes: parseInt(config.nombreGardes),
      mois: mois,
      annee: parseInt(annee)
    }));

    await prisma.gardeConfig.createMany({
      data: configData
    });

    res.json({ message: 'Configuration sauvegardée avec succès' });
  } catch (error) {
    console.error('Erreur lors de la sauvegarde de la configuration:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// POST /api/gardes/generer - Générer la liste des gardes
router.post('/generer', async (req, res) => {
  try {
    const { mois, annee } = req.body;

    if (!mois || !annee) {
      return res.status(400).json({ error: 'Mois et année requis' });
    }

    // Récupérer tous les médecins
    const medecins = await prisma.medecin.findMany();

    // Récupérer la configuration des gardes pour ce mois
    const configurations = await prisma.gardeConfig.findMany({
      where: {
        mois: mois,
        annee: parseInt(annee)
      }
    });

    if (configurations.length === 0) {
      return res.status(400).json({ error: 'Aucune configuration trouvée pour ce mois' });
    }

    // Générer les gardes
    const gardes = gardeGenerator.genererGardes(medecins, configurations, mois, parseInt(annee));

    // Supprimer les anciennes gardes pour ce mois
    await prisma.garde.deleteMany({
      where: {
        mois: mois,
        annee: parseInt(annee)
      }
    });

    // Sauvegarder les nouvelles gardes
    await prisma.garde.createMany({
      data: gardes
    });

    // Récupérer les gardes avec les informations des médecins
    const gardesAvecMedecins = await prisma.garde.findMany({
      where: {
        mois: mois,
        annee: parseInt(annee)
      },
      include: {
        medecinAssistant: true,
        medecinResidant: true
      },
      orderBy: {
        date: 'asc'
      }
    });

    res.json(gardesAvecMedecins);
  } catch (error) {
    console.error('Erreur lors de la génération des gardes:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// GET /api/gardes/:mois/:annee - Récupérer les gardes d'un mois
router.get('/:mois/:annee', async (req, res) => {
  try {
    const { mois, annee } = req.params;

    const gardes = await prisma.garde.findMany({
      where: {
        mois: mois,
        annee: parseInt(annee)
      },
      include: {
        medecinAssistant: true,
        medecinResidant: true
      },
      orderBy: {
        date: 'asc'
      }
    });

    res.json(gardes);
  } catch (error) {
    console.error('Erreur lors de la récupération des gardes:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// POST /api/gardes/save - Sauvegarder un planning complet
router.post('/save', async (req, res) => {
  try {
    const { gardes } = req.body;

    if (!gardes || !Array.isArray(gardes)) {
      return res.status(400).json({ error: 'Données de gardes invalides' });
    }

    if (gardes.length === 0) {
      return res.status(400).json({ error: 'Aucune garde à sauvegarder' });
    }

    const { mois, annee } = gardes[0];

    // Supprimer les anciennes gardes pour ce mois
    await prisma.garde.deleteMany({
      where: {
        mois: mois,
        annee: parseInt(annee)
      }
    });

    // Filtrer les gardes qui ont au moins un médecin assigné
    const gardesValides = gardes.filter(garde =>
      garde.medecinAssistantId || garde.medecinResidantId
    );

    if (gardesValides.length > 0) {
      // Sauvegarder les nouvelles gardes
      await prisma.garde.createMany({
        data: gardesValides.map(garde => ({
          date: garde.date,
          medecinAssistantId: garde.medecinAssistantId || null,
          medecinResidantId: garde.medecinResidantId || null,
          mois: garde.mois,
          annee: parseInt(garde.annee)
        }))
      });
    }

    res.json({
      message: 'Planning sauvegardé avec succès',
      gardesSauvegardees: gardesValides.length
    });

  } catch (error) {
    console.error('Erreur lors de la sauvegarde du planning:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// DELETE /api/gardes/:id - Supprimer une garde (date)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const gardeId = parseInt(id);

    if (isNaN(gardeId)) {
      return res.status(400).json({ error: 'ID invalide' });
    }

    await prisma.garde.delete({
      where: { id: gardeId }
    });

    res.json({ message: 'Garde supprimée avec succès' });
  } catch (error) {
    console.error('Erreur lors de la suppression de la garde:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// GET /api/gardes/export/:mois/:annee - Exporter les gardes en Word
router.get('/export/:mois/:annee', async (req, res) => {
  try {
    const { mois, annee } = req.params;

    const gardes = await prisma.garde.findMany({
      where: {
        mois: mois,
        annee: parseInt(annee)
      },
      include: {
        medecinAssistant: true,
        medecinResidant: true
      },
      orderBy: {
        date: 'asc'
      }
    });

    if (gardes.length === 0) {
      return res.status(404).json({ error: 'Aucune garde trouvée pour ce mois' });
    }

    // Créer le document Word
    const doc = await creerDocumentWord(gardes, mois, annee);
    const buffer = await Packer.toBuffer(doc);

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    res.setHeader('Content-Disposition', `attachment; filename=gardes-${mois}-${annee}.docx`);
    res.send(buffer);

  } catch (error) {
    console.error('Erreur lors de l\'exportation:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// Fonction pour créer le document Word
async function creerDocumentWord(gardes, mois, annee) {
  const rows = [
    // En-tête du tableau
    new TableRow({
      children: [
        new TableCell({
          children: [new Paragraph({ children: [new TextRun({ text: "Date", bold: true })] })],
          width: { size: 2000, type: WidthType.DXA }
        }),
        new TableCell({
          children: [new Paragraph({ children: [new TextRun({ text: "Jour", bold: true })] })],
          width: { size: 2000, type: WidthType.DXA }
        }),
        new TableCell({
          children: [new Paragraph({ children: [new TextRun({ text: "Assistant", bold: true })] })],
          width: { size: 3000, type: WidthType.DXA }
        }),
        new TableCell({
          children: [new Paragraph({ children: [new TextRun({ text: "Résidant", bold: true })] })],
          width: { size: 3000, type: WidthType.DXA }
        })
      ]
    })
  ];

  // Ajouter les lignes de données
  gardes.forEach(garde => {
    const assistantNom = garde.medecinAssistant
      ? `${garde.medecinAssistant.prenom} ${garde.medecinAssistant.nom}`
      : '-';

    const residantNom = garde.medecinResidant
      ? `${garde.medecinResidant.prenom} ${garde.medecinResidant.nom}`
      : '-';

    rows.push(
      new TableRow({
        children: [
          new TableCell({
            children: [new Paragraph(garde.date)]
          }),
          new TableCell({
            children: [new Paragraph(garde.jour || '')]
          }),
          new TableCell({
            children: [new Paragraph(assistantNom)]
          }),
          new TableCell({
            children: [new Paragraph(residantNom)]
          })
        ]
      })
    );
  });

  const table = new Table({
    rows: rows,
    width: { size: 100, type: WidthType.PERCENTAGE }
  });

  const doc = new Document({
    sections: [{
      children: [
        new Paragraph({
          children: [
            new TextRun({
              text: `Planning des Gardes - ${mois.charAt(0).toUpperCase() + mois.slice(1)} ${annee}`,
              bold: true,
              size: 28
            })
          ],
          spacing: { after: 400 }
        }),
        table
      ]
    }]
  });

  return doc;
}

module.exports = router;
