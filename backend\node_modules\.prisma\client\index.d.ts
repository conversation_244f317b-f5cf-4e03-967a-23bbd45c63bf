
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model Medecin
 * 
 */
export type Medecin = $Result.DefaultSelection<Prisma.$MedecinPayload>
/**
 * Model GardeConfig
 * 
 */
export type GardeConfig = $Result.DefaultSelection<Prisma.$GardeConfigPayload>
/**
 * Model Garde
 * 
 */
export type Garde = $Result.DefaultSelection<Prisma.$GardePayload>

/**
 * Enums
 */
export namespace $Enums {
  export const TypeMedecin: {
  Residant: 'Residant',
  Assistant: 'Assistant'
};

export type TypeMedecin = (typeof TypeMedecin)[keyof typeof TypeMedecin]

}

export type TypeMedecin = $Enums.TypeMedecin

export const TypeMedecin: typeof $Enums.TypeMedecin

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Medecins
 * const medecins = await prisma.medecin.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Medecins
   * const medecins = await prisma.medecin.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.medecin`: Exposes CRUD operations for the **Medecin** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Medecins
    * const medecins = await prisma.medecin.findMany()
    * ```
    */
  get medecin(): Prisma.MedecinDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.gardeConfig`: Exposes CRUD operations for the **GardeConfig** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more GardeConfigs
    * const gardeConfigs = await prisma.gardeConfig.findMany()
    * ```
    */
  get gardeConfig(): Prisma.GardeConfigDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.garde`: Exposes CRUD operations for the **Garde** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Gardes
    * const gardes = await prisma.garde.findMany()
    * ```
    */
  get garde(): Prisma.GardeDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.8.2
   * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    Medecin: 'Medecin',
    GardeConfig: 'GardeConfig',
    Garde: 'Garde'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "medecin" | "gardeConfig" | "garde"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      Medecin: {
        payload: Prisma.$MedecinPayload<ExtArgs>
        fields: Prisma.MedecinFieldRefs
        operations: {
          findUnique: {
            args: Prisma.MedecinFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MedecinPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.MedecinFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MedecinPayload>
          }
          findFirst: {
            args: Prisma.MedecinFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MedecinPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.MedecinFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MedecinPayload>
          }
          findMany: {
            args: Prisma.MedecinFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MedecinPayload>[]
          }
          create: {
            args: Prisma.MedecinCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MedecinPayload>
          }
          createMany: {
            args: Prisma.MedecinCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.MedecinCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MedecinPayload>[]
          }
          delete: {
            args: Prisma.MedecinDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MedecinPayload>
          }
          update: {
            args: Prisma.MedecinUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MedecinPayload>
          }
          deleteMany: {
            args: Prisma.MedecinDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.MedecinUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.MedecinUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MedecinPayload>[]
          }
          upsert: {
            args: Prisma.MedecinUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$MedecinPayload>
          }
          aggregate: {
            args: Prisma.MedecinAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateMedecin>
          }
          groupBy: {
            args: Prisma.MedecinGroupByArgs<ExtArgs>
            result: $Utils.Optional<MedecinGroupByOutputType>[]
          }
          count: {
            args: Prisma.MedecinCountArgs<ExtArgs>
            result: $Utils.Optional<MedecinCountAggregateOutputType> | number
          }
        }
      }
      GardeConfig: {
        payload: Prisma.$GardeConfigPayload<ExtArgs>
        fields: Prisma.GardeConfigFieldRefs
        operations: {
          findUnique: {
            args: Prisma.GardeConfigFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardeConfigPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.GardeConfigFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardeConfigPayload>
          }
          findFirst: {
            args: Prisma.GardeConfigFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardeConfigPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.GardeConfigFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardeConfigPayload>
          }
          findMany: {
            args: Prisma.GardeConfigFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardeConfigPayload>[]
          }
          create: {
            args: Prisma.GardeConfigCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardeConfigPayload>
          }
          createMany: {
            args: Prisma.GardeConfigCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.GardeConfigCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardeConfigPayload>[]
          }
          delete: {
            args: Prisma.GardeConfigDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardeConfigPayload>
          }
          update: {
            args: Prisma.GardeConfigUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardeConfigPayload>
          }
          deleteMany: {
            args: Prisma.GardeConfigDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.GardeConfigUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.GardeConfigUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardeConfigPayload>[]
          }
          upsert: {
            args: Prisma.GardeConfigUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardeConfigPayload>
          }
          aggregate: {
            args: Prisma.GardeConfigAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateGardeConfig>
          }
          groupBy: {
            args: Prisma.GardeConfigGroupByArgs<ExtArgs>
            result: $Utils.Optional<GardeConfigGroupByOutputType>[]
          }
          count: {
            args: Prisma.GardeConfigCountArgs<ExtArgs>
            result: $Utils.Optional<GardeConfigCountAggregateOutputType> | number
          }
        }
      }
      Garde: {
        payload: Prisma.$GardePayload<ExtArgs>
        fields: Prisma.GardeFieldRefs
        operations: {
          findUnique: {
            args: Prisma.GardeFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.GardeFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardePayload>
          }
          findFirst: {
            args: Prisma.GardeFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.GardeFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardePayload>
          }
          findMany: {
            args: Prisma.GardeFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardePayload>[]
          }
          create: {
            args: Prisma.GardeCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardePayload>
          }
          createMany: {
            args: Prisma.GardeCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.GardeCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardePayload>[]
          }
          delete: {
            args: Prisma.GardeDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardePayload>
          }
          update: {
            args: Prisma.GardeUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardePayload>
          }
          deleteMany: {
            args: Prisma.GardeDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.GardeUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.GardeUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardePayload>[]
          }
          upsert: {
            args: Prisma.GardeUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GardePayload>
          }
          aggregate: {
            args: Prisma.GardeAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateGarde>
          }
          groupBy: {
            args: Prisma.GardeGroupByArgs<ExtArgs>
            result: $Utils.Optional<GardeGroupByOutputType>[]
          }
          count: {
            args: Prisma.GardeCountArgs<ExtArgs>
            result: $Utils.Optional<GardeCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    medecin?: MedecinOmit
    gardeConfig?: GardeConfigOmit
    garde?: GardeOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type MedecinCountOutputType
   */

  export type MedecinCountOutputType = {
    gardesAssistant: number
    gardesEnTantQueAssistant: number
    gardesEnTantQueResidant: number
  }

  export type MedecinCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    gardesAssistant?: boolean | MedecinCountOutputTypeCountGardesAssistantArgs
    gardesEnTantQueAssistant?: boolean | MedecinCountOutputTypeCountGardesEnTantQueAssistantArgs
    gardesEnTantQueResidant?: boolean | MedecinCountOutputTypeCountGardesEnTantQueResidantArgs
  }

  // Custom InputTypes
  /**
   * MedecinCountOutputType without action
   */
  export type MedecinCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the MedecinCountOutputType
     */
    select?: MedecinCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * MedecinCountOutputType without action
   */
  export type MedecinCountOutputTypeCountGardesAssistantArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: GardeConfigWhereInput
  }

  /**
   * MedecinCountOutputType without action
   */
  export type MedecinCountOutputTypeCountGardesEnTantQueAssistantArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: GardeWhereInput
  }

  /**
   * MedecinCountOutputType without action
   */
  export type MedecinCountOutputTypeCountGardesEnTantQueResidantArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: GardeWhereInput
  }


  /**
   * Models
   */

  /**
   * Model Medecin
   */

  export type AggregateMedecin = {
    _count: MedecinCountAggregateOutputType | null
    _avg: MedecinAvgAggregateOutputType | null
    _sum: MedecinSumAggregateOutputType | null
    _min: MedecinMinAggregateOutputType | null
    _max: MedecinMaxAggregateOutputType | null
  }

  export type MedecinAvgAggregateOutputType = {
    id: number | null
  }

  export type MedecinSumAggregateOutputType = {
    id: number | null
  }

  export type MedecinMinAggregateOutputType = {
    id: number | null
    nom: string | null
    prenom: string | null
    type: $Enums.TypeMedecin | null
    createdAt: Date | null
  }

  export type MedecinMaxAggregateOutputType = {
    id: number | null
    nom: string | null
    prenom: string | null
    type: $Enums.TypeMedecin | null
    createdAt: Date | null
  }

  export type MedecinCountAggregateOutputType = {
    id: number
    nom: number
    prenom: number
    type: number
    createdAt: number
    _all: number
  }


  export type MedecinAvgAggregateInputType = {
    id?: true
  }

  export type MedecinSumAggregateInputType = {
    id?: true
  }

  export type MedecinMinAggregateInputType = {
    id?: true
    nom?: true
    prenom?: true
    type?: true
    createdAt?: true
  }

  export type MedecinMaxAggregateInputType = {
    id?: true
    nom?: true
    prenom?: true
    type?: true
    createdAt?: true
  }

  export type MedecinCountAggregateInputType = {
    id?: true
    nom?: true
    prenom?: true
    type?: true
    createdAt?: true
    _all?: true
  }

  export type MedecinAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Medecin to aggregate.
     */
    where?: MedecinWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Medecins to fetch.
     */
    orderBy?: MedecinOrderByWithRelationInput | MedecinOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: MedecinWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Medecins from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Medecins.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Medecins
    **/
    _count?: true | MedecinCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: MedecinAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: MedecinSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: MedecinMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: MedecinMaxAggregateInputType
  }

  export type GetMedecinAggregateType<T extends MedecinAggregateArgs> = {
        [P in keyof T & keyof AggregateMedecin]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateMedecin[P]>
      : GetScalarType<T[P], AggregateMedecin[P]>
  }




  export type MedecinGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: MedecinWhereInput
    orderBy?: MedecinOrderByWithAggregationInput | MedecinOrderByWithAggregationInput[]
    by: MedecinScalarFieldEnum[] | MedecinScalarFieldEnum
    having?: MedecinScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: MedecinCountAggregateInputType | true
    _avg?: MedecinAvgAggregateInputType
    _sum?: MedecinSumAggregateInputType
    _min?: MedecinMinAggregateInputType
    _max?: MedecinMaxAggregateInputType
  }

  export type MedecinGroupByOutputType = {
    id: number
    nom: string
    prenom: string
    type: $Enums.TypeMedecin
    createdAt: Date
    _count: MedecinCountAggregateOutputType | null
    _avg: MedecinAvgAggregateOutputType | null
    _sum: MedecinSumAggregateOutputType | null
    _min: MedecinMinAggregateOutputType | null
    _max: MedecinMaxAggregateOutputType | null
  }

  type GetMedecinGroupByPayload<T extends MedecinGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<MedecinGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof MedecinGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], MedecinGroupByOutputType[P]>
            : GetScalarType<T[P], MedecinGroupByOutputType[P]>
        }
      >
    >


  export type MedecinSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    nom?: boolean
    prenom?: boolean
    type?: boolean
    createdAt?: boolean
    gardesAssistant?: boolean | Medecin$gardesAssistantArgs<ExtArgs>
    gardesEnTantQueAssistant?: boolean | Medecin$gardesEnTantQueAssistantArgs<ExtArgs>
    gardesEnTantQueResidant?: boolean | Medecin$gardesEnTantQueResidantArgs<ExtArgs>
    _count?: boolean | MedecinCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["medecin"]>

  export type MedecinSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    nom?: boolean
    prenom?: boolean
    type?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["medecin"]>

  export type MedecinSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    nom?: boolean
    prenom?: boolean
    type?: boolean
    createdAt?: boolean
  }, ExtArgs["result"]["medecin"]>

  export type MedecinSelectScalar = {
    id?: boolean
    nom?: boolean
    prenom?: boolean
    type?: boolean
    createdAt?: boolean
  }

  export type MedecinOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "nom" | "prenom" | "type" | "createdAt", ExtArgs["result"]["medecin"]>
  export type MedecinInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    gardesAssistant?: boolean | Medecin$gardesAssistantArgs<ExtArgs>
    gardesEnTantQueAssistant?: boolean | Medecin$gardesEnTantQueAssistantArgs<ExtArgs>
    gardesEnTantQueResidant?: boolean | Medecin$gardesEnTantQueResidantArgs<ExtArgs>
    _count?: boolean | MedecinCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type MedecinIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type MedecinIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $MedecinPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Medecin"
    objects: {
      gardesAssistant: Prisma.$GardeConfigPayload<ExtArgs>[]
      gardesEnTantQueAssistant: Prisma.$GardePayload<ExtArgs>[]
      gardesEnTantQueResidant: Prisma.$GardePayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      nom: string
      prenom: string
      type: $Enums.TypeMedecin
      createdAt: Date
    }, ExtArgs["result"]["medecin"]>
    composites: {}
  }

  type MedecinGetPayload<S extends boolean | null | undefined | MedecinDefaultArgs> = $Result.GetResult<Prisma.$MedecinPayload, S>

  type MedecinCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<MedecinFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: MedecinCountAggregateInputType | true
    }

  export interface MedecinDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Medecin'], meta: { name: 'Medecin' } }
    /**
     * Find zero or one Medecin that matches the filter.
     * @param {MedecinFindUniqueArgs} args - Arguments to find a Medecin
     * @example
     * // Get one Medecin
     * const medecin = await prisma.medecin.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends MedecinFindUniqueArgs>(args: SelectSubset<T, MedecinFindUniqueArgs<ExtArgs>>): Prisma__MedecinClient<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Medecin that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {MedecinFindUniqueOrThrowArgs} args - Arguments to find a Medecin
     * @example
     * // Get one Medecin
     * const medecin = await prisma.medecin.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends MedecinFindUniqueOrThrowArgs>(args: SelectSubset<T, MedecinFindUniqueOrThrowArgs<ExtArgs>>): Prisma__MedecinClient<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Medecin that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MedecinFindFirstArgs} args - Arguments to find a Medecin
     * @example
     * // Get one Medecin
     * const medecin = await prisma.medecin.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends MedecinFindFirstArgs>(args?: SelectSubset<T, MedecinFindFirstArgs<ExtArgs>>): Prisma__MedecinClient<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Medecin that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MedecinFindFirstOrThrowArgs} args - Arguments to find a Medecin
     * @example
     * // Get one Medecin
     * const medecin = await prisma.medecin.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends MedecinFindFirstOrThrowArgs>(args?: SelectSubset<T, MedecinFindFirstOrThrowArgs<ExtArgs>>): Prisma__MedecinClient<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Medecins that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MedecinFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Medecins
     * const medecins = await prisma.medecin.findMany()
     * 
     * // Get first 10 Medecins
     * const medecins = await prisma.medecin.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const medecinWithIdOnly = await prisma.medecin.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends MedecinFindManyArgs>(args?: SelectSubset<T, MedecinFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Medecin.
     * @param {MedecinCreateArgs} args - Arguments to create a Medecin.
     * @example
     * // Create one Medecin
     * const Medecin = await prisma.medecin.create({
     *   data: {
     *     // ... data to create a Medecin
     *   }
     * })
     * 
     */
    create<T extends MedecinCreateArgs>(args: SelectSubset<T, MedecinCreateArgs<ExtArgs>>): Prisma__MedecinClient<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Medecins.
     * @param {MedecinCreateManyArgs} args - Arguments to create many Medecins.
     * @example
     * // Create many Medecins
     * const medecin = await prisma.medecin.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends MedecinCreateManyArgs>(args?: SelectSubset<T, MedecinCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Medecins and returns the data saved in the database.
     * @param {MedecinCreateManyAndReturnArgs} args - Arguments to create many Medecins.
     * @example
     * // Create many Medecins
     * const medecin = await prisma.medecin.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Medecins and only return the `id`
     * const medecinWithIdOnly = await prisma.medecin.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends MedecinCreateManyAndReturnArgs>(args?: SelectSubset<T, MedecinCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Medecin.
     * @param {MedecinDeleteArgs} args - Arguments to delete one Medecin.
     * @example
     * // Delete one Medecin
     * const Medecin = await prisma.medecin.delete({
     *   where: {
     *     // ... filter to delete one Medecin
     *   }
     * })
     * 
     */
    delete<T extends MedecinDeleteArgs>(args: SelectSubset<T, MedecinDeleteArgs<ExtArgs>>): Prisma__MedecinClient<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Medecin.
     * @param {MedecinUpdateArgs} args - Arguments to update one Medecin.
     * @example
     * // Update one Medecin
     * const medecin = await prisma.medecin.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends MedecinUpdateArgs>(args: SelectSubset<T, MedecinUpdateArgs<ExtArgs>>): Prisma__MedecinClient<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Medecins.
     * @param {MedecinDeleteManyArgs} args - Arguments to filter Medecins to delete.
     * @example
     * // Delete a few Medecins
     * const { count } = await prisma.medecin.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends MedecinDeleteManyArgs>(args?: SelectSubset<T, MedecinDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Medecins.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MedecinUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Medecins
     * const medecin = await prisma.medecin.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends MedecinUpdateManyArgs>(args: SelectSubset<T, MedecinUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Medecins and returns the data updated in the database.
     * @param {MedecinUpdateManyAndReturnArgs} args - Arguments to update many Medecins.
     * @example
     * // Update many Medecins
     * const medecin = await prisma.medecin.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Medecins and only return the `id`
     * const medecinWithIdOnly = await prisma.medecin.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends MedecinUpdateManyAndReturnArgs>(args: SelectSubset<T, MedecinUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Medecin.
     * @param {MedecinUpsertArgs} args - Arguments to update or create a Medecin.
     * @example
     * // Update or create a Medecin
     * const medecin = await prisma.medecin.upsert({
     *   create: {
     *     // ... data to create a Medecin
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Medecin we want to update
     *   }
     * })
     */
    upsert<T extends MedecinUpsertArgs>(args: SelectSubset<T, MedecinUpsertArgs<ExtArgs>>): Prisma__MedecinClient<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Medecins.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MedecinCountArgs} args - Arguments to filter Medecins to count.
     * @example
     * // Count the number of Medecins
     * const count = await prisma.medecin.count({
     *   where: {
     *     // ... the filter for the Medecins we want to count
     *   }
     * })
    **/
    count<T extends MedecinCountArgs>(
      args?: Subset<T, MedecinCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], MedecinCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Medecin.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MedecinAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends MedecinAggregateArgs>(args: Subset<T, MedecinAggregateArgs>): Prisma.PrismaPromise<GetMedecinAggregateType<T>>

    /**
     * Group by Medecin.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {MedecinGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends MedecinGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: MedecinGroupByArgs['orderBy'] }
        : { orderBy?: MedecinGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, MedecinGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetMedecinGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Medecin model
   */
  readonly fields: MedecinFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Medecin.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__MedecinClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    gardesAssistant<T extends Medecin$gardesAssistantArgs<ExtArgs> = {}>(args?: Subset<T, Medecin$gardesAssistantArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GardeConfigPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    gardesEnTantQueAssistant<T extends Medecin$gardesEnTantQueAssistantArgs<ExtArgs> = {}>(args?: Subset<T, Medecin$gardesEnTantQueAssistantArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GardePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    gardesEnTantQueResidant<T extends Medecin$gardesEnTantQueResidantArgs<ExtArgs> = {}>(args?: Subset<T, Medecin$gardesEnTantQueResidantArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GardePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Medecin model
   */
  interface MedecinFieldRefs {
    readonly id: FieldRef<"Medecin", 'Int'>
    readonly nom: FieldRef<"Medecin", 'String'>
    readonly prenom: FieldRef<"Medecin", 'String'>
    readonly type: FieldRef<"Medecin", 'TypeMedecin'>
    readonly createdAt: FieldRef<"Medecin", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Medecin findUnique
   */
  export type MedecinFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MedecinInclude<ExtArgs> | null
    /**
     * Filter, which Medecin to fetch.
     */
    where: MedecinWhereUniqueInput
  }

  /**
   * Medecin findUniqueOrThrow
   */
  export type MedecinFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MedecinInclude<ExtArgs> | null
    /**
     * Filter, which Medecin to fetch.
     */
    where: MedecinWhereUniqueInput
  }

  /**
   * Medecin findFirst
   */
  export type MedecinFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MedecinInclude<ExtArgs> | null
    /**
     * Filter, which Medecin to fetch.
     */
    where?: MedecinWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Medecins to fetch.
     */
    orderBy?: MedecinOrderByWithRelationInput | MedecinOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Medecins.
     */
    cursor?: MedecinWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Medecins from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Medecins.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Medecins.
     */
    distinct?: MedecinScalarFieldEnum | MedecinScalarFieldEnum[]
  }

  /**
   * Medecin findFirstOrThrow
   */
  export type MedecinFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MedecinInclude<ExtArgs> | null
    /**
     * Filter, which Medecin to fetch.
     */
    where?: MedecinWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Medecins to fetch.
     */
    orderBy?: MedecinOrderByWithRelationInput | MedecinOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Medecins.
     */
    cursor?: MedecinWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Medecins from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Medecins.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Medecins.
     */
    distinct?: MedecinScalarFieldEnum | MedecinScalarFieldEnum[]
  }

  /**
   * Medecin findMany
   */
  export type MedecinFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MedecinInclude<ExtArgs> | null
    /**
     * Filter, which Medecins to fetch.
     */
    where?: MedecinWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Medecins to fetch.
     */
    orderBy?: MedecinOrderByWithRelationInput | MedecinOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Medecins.
     */
    cursor?: MedecinWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Medecins from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Medecins.
     */
    skip?: number
    distinct?: MedecinScalarFieldEnum | MedecinScalarFieldEnum[]
  }

  /**
   * Medecin create
   */
  export type MedecinCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MedecinInclude<ExtArgs> | null
    /**
     * The data needed to create a Medecin.
     */
    data: XOR<MedecinCreateInput, MedecinUncheckedCreateInput>
  }

  /**
   * Medecin createMany
   */
  export type MedecinCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Medecins.
     */
    data: MedecinCreateManyInput | MedecinCreateManyInput[]
  }

  /**
   * Medecin createManyAndReturn
   */
  export type MedecinCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * The data used to create many Medecins.
     */
    data: MedecinCreateManyInput | MedecinCreateManyInput[]
  }

  /**
   * Medecin update
   */
  export type MedecinUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MedecinInclude<ExtArgs> | null
    /**
     * The data needed to update a Medecin.
     */
    data: XOR<MedecinUpdateInput, MedecinUncheckedUpdateInput>
    /**
     * Choose, which Medecin to update.
     */
    where: MedecinWhereUniqueInput
  }

  /**
   * Medecin updateMany
   */
  export type MedecinUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Medecins.
     */
    data: XOR<MedecinUpdateManyMutationInput, MedecinUncheckedUpdateManyInput>
    /**
     * Filter which Medecins to update
     */
    where?: MedecinWhereInput
    /**
     * Limit how many Medecins to update.
     */
    limit?: number
  }

  /**
   * Medecin updateManyAndReturn
   */
  export type MedecinUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * The data used to update Medecins.
     */
    data: XOR<MedecinUpdateManyMutationInput, MedecinUncheckedUpdateManyInput>
    /**
     * Filter which Medecins to update
     */
    where?: MedecinWhereInput
    /**
     * Limit how many Medecins to update.
     */
    limit?: number
  }

  /**
   * Medecin upsert
   */
  export type MedecinUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MedecinInclude<ExtArgs> | null
    /**
     * The filter to search for the Medecin to update in case it exists.
     */
    where: MedecinWhereUniqueInput
    /**
     * In case the Medecin found by the `where` argument doesn't exist, create a new Medecin with this data.
     */
    create: XOR<MedecinCreateInput, MedecinUncheckedCreateInput>
    /**
     * In case the Medecin was found with the provided `where` argument, update it with this data.
     */
    update: XOR<MedecinUpdateInput, MedecinUncheckedUpdateInput>
  }

  /**
   * Medecin delete
   */
  export type MedecinDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MedecinInclude<ExtArgs> | null
    /**
     * Filter which Medecin to delete.
     */
    where: MedecinWhereUniqueInput
  }

  /**
   * Medecin deleteMany
   */
  export type MedecinDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Medecins to delete
     */
    where?: MedecinWhereInput
    /**
     * Limit how many Medecins to delete.
     */
    limit?: number
  }

  /**
   * Medecin.gardesAssistant
   */
  export type Medecin$gardesAssistantArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GardeConfig
     */
    select?: GardeConfigSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GardeConfig
     */
    omit?: GardeConfigOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeConfigInclude<ExtArgs> | null
    where?: GardeConfigWhereInput
    orderBy?: GardeConfigOrderByWithRelationInput | GardeConfigOrderByWithRelationInput[]
    cursor?: GardeConfigWhereUniqueInput
    take?: number
    skip?: number
    distinct?: GardeConfigScalarFieldEnum | GardeConfigScalarFieldEnum[]
  }

  /**
   * Medecin.gardesEnTantQueAssistant
   */
  export type Medecin$gardesEnTantQueAssistantArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeInclude<ExtArgs> | null
    where?: GardeWhereInput
    orderBy?: GardeOrderByWithRelationInput | GardeOrderByWithRelationInput[]
    cursor?: GardeWhereUniqueInput
    take?: number
    skip?: number
    distinct?: GardeScalarFieldEnum | GardeScalarFieldEnum[]
  }

  /**
   * Medecin.gardesEnTantQueResidant
   */
  export type Medecin$gardesEnTantQueResidantArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeInclude<ExtArgs> | null
    where?: GardeWhereInput
    orderBy?: GardeOrderByWithRelationInput | GardeOrderByWithRelationInput[]
    cursor?: GardeWhereUniqueInput
    take?: number
    skip?: number
    distinct?: GardeScalarFieldEnum | GardeScalarFieldEnum[]
  }

  /**
   * Medecin without action
   */
  export type MedecinDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MedecinInclude<ExtArgs> | null
  }


  /**
   * Model GardeConfig
   */

  export type AggregateGardeConfig = {
    _count: GardeConfigCountAggregateOutputType | null
    _avg: GardeConfigAvgAggregateOutputType | null
    _sum: GardeConfigSumAggregateOutputType | null
    _min: GardeConfigMinAggregateOutputType | null
    _max: GardeConfigMaxAggregateOutputType | null
  }

  export type GardeConfigAvgAggregateOutputType = {
    id: number | null
    medecinId: number | null
    nombreGardes: number | null
    annee: number | null
  }

  export type GardeConfigSumAggregateOutputType = {
    id: number | null
    medecinId: number | null
    nombreGardes: number | null
    annee: number | null
  }

  export type GardeConfigMinAggregateOutputType = {
    id: number | null
    medecinId: number | null
    nombreGardes: number | null
    mois: string | null
    annee: number | null
  }

  export type GardeConfigMaxAggregateOutputType = {
    id: number | null
    medecinId: number | null
    nombreGardes: number | null
    mois: string | null
    annee: number | null
  }

  export type GardeConfigCountAggregateOutputType = {
    id: number
    medecinId: number
    nombreGardes: number
    mois: number
    annee: number
    _all: number
  }


  export type GardeConfigAvgAggregateInputType = {
    id?: true
    medecinId?: true
    nombreGardes?: true
    annee?: true
  }

  export type GardeConfigSumAggregateInputType = {
    id?: true
    medecinId?: true
    nombreGardes?: true
    annee?: true
  }

  export type GardeConfigMinAggregateInputType = {
    id?: true
    medecinId?: true
    nombreGardes?: true
    mois?: true
    annee?: true
  }

  export type GardeConfigMaxAggregateInputType = {
    id?: true
    medecinId?: true
    nombreGardes?: true
    mois?: true
    annee?: true
  }

  export type GardeConfigCountAggregateInputType = {
    id?: true
    medecinId?: true
    nombreGardes?: true
    mois?: true
    annee?: true
    _all?: true
  }

  export type GardeConfigAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which GardeConfig to aggregate.
     */
    where?: GardeConfigWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GardeConfigs to fetch.
     */
    orderBy?: GardeConfigOrderByWithRelationInput | GardeConfigOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: GardeConfigWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GardeConfigs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GardeConfigs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned GardeConfigs
    **/
    _count?: true | GardeConfigCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: GardeConfigAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: GardeConfigSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: GardeConfigMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: GardeConfigMaxAggregateInputType
  }

  export type GetGardeConfigAggregateType<T extends GardeConfigAggregateArgs> = {
        [P in keyof T & keyof AggregateGardeConfig]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateGardeConfig[P]>
      : GetScalarType<T[P], AggregateGardeConfig[P]>
  }




  export type GardeConfigGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: GardeConfigWhereInput
    orderBy?: GardeConfigOrderByWithAggregationInput | GardeConfigOrderByWithAggregationInput[]
    by: GardeConfigScalarFieldEnum[] | GardeConfigScalarFieldEnum
    having?: GardeConfigScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: GardeConfigCountAggregateInputType | true
    _avg?: GardeConfigAvgAggregateInputType
    _sum?: GardeConfigSumAggregateInputType
    _min?: GardeConfigMinAggregateInputType
    _max?: GardeConfigMaxAggregateInputType
  }

  export type GardeConfigGroupByOutputType = {
    id: number
    medecinId: number
    nombreGardes: number
    mois: string
    annee: number
    _count: GardeConfigCountAggregateOutputType | null
    _avg: GardeConfigAvgAggregateOutputType | null
    _sum: GardeConfigSumAggregateOutputType | null
    _min: GardeConfigMinAggregateOutputType | null
    _max: GardeConfigMaxAggregateOutputType | null
  }

  type GetGardeConfigGroupByPayload<T extends GardeConfigGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<GardeConfigGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof GardeConfigGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], GardeConfigGroupByOutputType[P]>
            : GetScalarType<T[P], GardeConfigGroupByOutputType[P]>
        }
      >
    >


  export type GardeConfigSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    medecinId?: boolean
    nombreGardes?: boolean
    mois?: boolean
    annee?: boolean
    medecin?: boolean | MedecinDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["gardeConfig"]>

  export type GardeConfigSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    medecinId?: boolean
    nombreGardes?: boolean
    mois?: boolean
    annee?: boolean
    medecin?: boolean | MedecinDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["gardeConfig"]>

  export type GardeConfigSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    medecinId?: boolean
    nombreGardes?: boolean
    mois?: boolean
    annee?: boolean
    medecin?: boolean | MedecinDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["gardeConfig"]>

  export type GardeConfigSelectScalar = {
    id?: boolean
    medecinId?: boolean
    nombreGardes?: boolean
    mois?: boolean
    annee?: boolean
  }

  export type GardeConfigOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "medecinId" | "nombreGardes" | "mois" | "annee", ExtArgs["result"]["gardeConfig"]>
  export type GardeConfigInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    medecin?: boolean | MedecinDefaultArgs<ExtArgs>
  }
  export type GardeConfigIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    medecin?: boolean | MedecinDefaultArgs<ExtArgs>
  }
  export type GardeConfigIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    medecin?: boolean | MedecinDefaultArgs<ExtArgs>
  }

  export type $GardeConfigPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "GardeConfig"
    objects: {
      medecin: Prisma.$MedecinPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      medecinId: number
      nombreGardes: number
      mois: string
      annee: number
    }, ExtArgs["result"]["gardeConfig"]>
    composites: {}
  }

  type GardeConfigGetPayload<S extends boolean | null | undefined | GardeConfigDefaultArgs> = $Result.GetResult<Prisma.$GardeConfigPayload, S>

  type GardeConfigCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<GardeConfigFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: GardeConfigCountAggregateInputType | true
    }

  export interface GardeConfigDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['GardeConfig'], meta: { name: 'GardeConfig' } }
    /**
     * Find zero or one GardeConfig that matches the filter.
     * @param {GardeConfigFindUniqueArgs} args - Arguments to find a GardeConfig
     * @example
     * // Get one GardeConfig
     * const gardeConfig = await prisma.gardeConfig.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends GardeConfigFindUniqueArgs>(args: SelectSubset<T, GardeConfigFindUniqueArgs<ExtArgs>>): Prisma__GardeConfigClient<$Result.GetResult<Prisma.$GardeConfigPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one GardeConfig that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {GardeConfigFindUniqueOrThrowArgs} args - Arguments to find a GardeConfig
     * @example
     * // Get one GardeConfig
     * const gardeConfig = await prisma.gardeConfig.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends GardeConfigFindUniqueOrThrowArgs>(args: SelectSubset<T, GardeConfigFindUniqueOrThrowArgs<ExtArgs>>): Prisma__GardeConfigClient<$Result.GetResult<Prisma.$GardeConfigPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first GardeConfig that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeConfigFindFirstArgs} args - Arguments to find a GardeConfig
     * @example
     * // Get one GardeConfig
     * const gardeConfig = await prisma.gardeConfig.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends GardeConfigFindFirstArgs>(args?: SelectSubset<T, GardeConfigFindFirstArgs<ExtArgs>>): Prisma__GardeConfigClient<$Result.GetResult<Prisma.$GardeConfigPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first GardeConfig that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeConfigFindFirstOrThrowArgs} args - Arguments to find a GardeConfig
     * @example
     * // Get one GardeConfig
     * const gardeConfig = await prisma.gardeConfig.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends GardeConfigFindFirstOrThrowArgs>(args?: SelectSubset<T, GardeConfigFindFirstOrThrowArgs<ExtArgs>>): Prisma__GardeConfigClient<$Result.GetResult<Prisma.$GardeConfigPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more GardeConfigs that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeConfigFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all GardeConfigs
     * const gardeConfigs = await prisma.gardeConfig.findMany()
     * 
     * // Get first 10 GardeConfigs
     * const gardeConfigs = await prisma.gardeConfig.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const gardeConfigWithIdOnly = await prisma.gardeConfig.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends GardeConfigFindManyArgs>(args?: SelectSubset<T, GardeConfigFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GardeConfigPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a GardeConfig.
     * @param {GardeConfigCreateArgs} args - Arguments to create a GardeConfig.
     * @example
     * // Create one GardeConfig
     * const GardeConfig = await prisma.gardeConfig.create({
     *   data: {
     *     // ... data to create a GardeConfig
     *   }
     * })
     * 
     */
    create<T extends GardeConfigCreateArgs>(args: SelectSubset<T, GardeConfigCreateArgs<ExtArgs>>): Prisma__GardeConfigClient<$Result.GetResult<Prisma.$GardeConfigPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many GardeConfigs.
     * @param {GardeConfigCreateManyArgs} args - Arguments to create many GardeConfigs.
     * @example
     * // Create many GardeConfigs
     * const gardeConfig = await prisma.gardeConfig.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends GardeConfigCreateManyArgs>(args?: SelectSubset<T, GardeConfigCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many GardeConfigs and returns the data saved in the database.
     * @param {GardeConfigCreateManyAndReturnArgs} args - Arguments to create many GardeConfigs.
     * @example
     * // Create many GardeConfigs
     * const gardeConfig = await prisma.gardeConfig.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many GardeConfigs and only return the `id`
     * const gardeConfigWithIdOnly = await prisma.gardeConfig.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends GardeConfigCreateManyAndReturnArgs>(args?: SelectSubset<T, GardeConfigCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GardeConfigPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a GardeConfig.
     * @param {GardeConfigDeleteArgs} args - Arguments to delete one GardeConfig.
     * @example
     * // Delete one GardeConfig
     * const GardeConfig = await prisma.gardeConfig.delete({
     *   where: {
     *     // ... filter to delete one GardeConfig
     *   }
     * })
     * 
     */
    delete<T extends GardeConfigDeleteArgs>(args: SelectSubset<T, GardeConfigDeleteArgs<ExtArgs>>): Prisma__GardeConfigClient<$Result.GetResult<Prisma.$GardeConfigPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one GardeConfig.
     * @param {GardeConfigUpdateArgs} args - Arguments to update one GardeConfig.
     * @example
     * // Update one GardeConfig
     * const gardeConfig = await prisma.gardeConfig.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends GardeConfigUpdateArgs>(args: SelectSubset<T, GardeConfigUpdateArgs<ExtArgs>>): Prisma__GardeConfigClient<$Result.GetResult<Prisma.$GardeConfigPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more GardeConfigs.
     * @param {GardeConfigDeleteManyArgs} args - Arguments to filter GardeConfigs to delete.
     * @example
     * // Delete a few GardeConfigs
     * const { count } = await prisma.gardeConfig.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends GardeConfigDeleteManyArgs>(args?: SelectSubset<T, GardeConfigDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more GardeConfigs.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeConfigUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many GardeConfigs
     * const gardeConfig = await prisma.gardeConfig.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends GardeConfigUpdateManyArgs>(args: SelectSubset<T, GardeConfigUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more GardeConfigs and returns the data updated in the database.
     * @param {GardeConfigUpdateManyAndReturnArgs} args - Arguments to update many GardeConfigs.
     * @example
     * // Update many GardeConfigs
     * const gardeConfig = await prisma.gardeConfig.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more GardeConfigs and only return the `id`
     * const gardeConfigWithIdOnly = await prisma.gardeConfig.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends GardeConfigUpdateManyAndReturnArgs>(args: SelectSubset<T, GardeConfigUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GardeConfigPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one GardeConfig.
     * @param {GardeConfigUpsertArgs} args - Arguments to update or create a GardeConfig.
     * @example
     * // Update or create a GardeConfig
     * const gardeConfig = await prisma.gardeConfig.upsert({
     *   create: {
     *     // ... data to create a GardeConfig
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the GardeConfig we want to update
     *   }
     * })
     */
    upsert<T extends GardeConfigUpsertArgs>(args: SelectSubset<T, GardeConfigUpsertArgs<ExtArgs>>): Prisma__GardeConfigClient<$Result.GetResult<Prisma.$GardeConfigPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of GardeConfigs.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeConfigCountArgs} args - Arguments to filter GardeConfigs to count.
     * @example
     * // Count the number of GardeConfigs
     * const count = await prisma.gardeConfig.count({
     *   where: {
     *     // ... the filter for the GardeConfigs we want to count
     *   }
     * })
    **/
    count<T extends GardeConfigCountArgs>(
      args?: Subset<T, GardeConfigCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], GardeConfigCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a GardeConfig.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeConfigAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends GardeConfigAggregateArgs>(args: Subset<T, GardeConfigAggregateArgs>): Prisma.PrismaPromise<GetGardeConfigAggregateType<T>>

    /**
     * Group by GardeConfig.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeConfigGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends GardeConfigGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: GardeConfigGroupByArgs['orderBy'] }
        : { orderBy?: GardeConfigGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, GardeConfigGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetGardeConfigGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the GardeConfig model
   */
  readonly fields: GardeConfigFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for GardeConfig.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__GardeConfigClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    medecin<T extends MedecinDefaultArgs<ExtArgs> = {}>(args?: Subset<T, MedecinDefaultArgs<ExtArgs>>): Prisma__MedecinClient<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the GardeConfig model
   */
  interface GardeConfigFieldRefs {
    readonly id: FieldRef<"GardeConfig", 'Int'>
    readonly medecinId: FieldRef<"GardeConfig", 'Int'>
    readonly nombreGardes: FieldRef<"GardeConfig", 'Int'>
    readonly mois: FieldRef<"GardeConfig", 'String'>
    readonly annee: FieldRef<"GardeConfig", 'Int'>
  }
    

  // Custom InputTypes
  /**
   * GardeConfig findUnique
   */
  export type GardeConfigFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GardeConfig
     */
    select?: GardeConfigSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GardeConfig
     */
    omit?: GardeConfigOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeConfigInclude<ExtArgs> | null
    /**
     * Filter, which GardeConfig to fetch.
     */
    where: GardeConfigWhereUniqueInput
  }

  /**
   * GardeConfig findUniqueOrThrow
   */
  export type GardeConfigFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GardeConfig
     */
    select?: GardeConfigSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GardeConfig
     */
    omit?: GardeConfigOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeConfigInclude<ExtArgs> | null
    /**
     * Filter, which GardeConfig to fetch.
     */
    where: GardeConfigWhereUniqueInput
  }

  /**
   * GardeConfig findFirst
   */
  export type GardeConfigFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GardeConfig
     */
    select?: GardeConfigSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GardeConfig
     */
    omit?: GardeConfigOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeConfigInclude<ExtArgs> | null
    /**
     * Filter, which GardeConfig to fetch.
     */
    where?: GardeConfigWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GardeConfigs to fetch.
     */
    orderBy?: GardeConfigOrderByWithRelationInput | GardeConfigOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for GardeConfigs.
     */
    cursor?: GardeConfigWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GardeConfigs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GardeConfigs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of GardeConfigs.
     */
    distinct?: GardeConfigScalarFieldEnum | GardeConfigScalarFieldEnum[]
  }

  /**
   * GardeConfig findFirstOrThrow
   */
  export type GardeConfigFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GardeConfig
     */
    select?: GardeConfigSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GardeConfig
     */
    omit?: GardeConfigOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeConfigInclude<ExtArgs> | null
    /**
     * Filter, which GardeConfig to fetch.
     */
    where?: GardeConfigWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GardeConfigs to fetch.
     */
    orderBy?: GardeConfigOrderByWithRelationInput | GardeConfigOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for GardeConfigs.
     */
    cursor?: GardeConfigWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GardeConfigs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GardeConfigs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of GardeConfigs.
     */
    distinct?: GardeConfigScalarFieldEnum | GardeConfigScalarFieldEnum[]
  }

  /**
   * GardeConfig findMany
   */
  export type GardeConfigFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GardeConfig
     */
    select?: GardeConfigSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GardeConfig
     */
    omit?: GardeConfigOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeConfigInclude<ExtArgs> | null
    /**
     * Filter, which GardeConfigs to fetch.
     */
    where?: GardeConfigWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GardeConfigs to fetch.
     */
    orderBy?: GardeConfigOrderByWithRelationInput | GardeConfigOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing GardeConfigs.
     */
    cursor?: GardeConfigWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GardeConfigs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GardeConfigs.
     */
    skip?: number
    distinct?: GardeConfigScalarFieldEnum | GardeConfigScalarFieldEnum[]
  }

  /**
   * GardeConfig create
   */
  export type GardeConfigCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GardeConfig
     */
    select?: GardeConfigSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GardeConfig
     */
    omit?: GardeConfigOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeConfigInclude<ExtArgs> | null
    /**
     * The data needed to create a GardeConfig.
     */
    data: XOR<GardeConfigCreateInput, GardeConfigUncheckedCreateInput>
  }

  /**
   * GardeConfig createMany
   */
  export type GardeConfigCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many GardeConfigs.
     */
    data: GardeConfigCreateManyInput | GardeConfigCreateManyInput[]
  }

  /**
   * GardeConfig createManyAndReturn
   */
  export type GardeConfigCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GardeConfig
     */
    select?: GardeConfigSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the GardeConfig
     */
    omit?: GardeConfigOmit<ExtArgs> | null
    /**
     * The data used to create many GardeConfigs.
     */
    data: GardeConfigCreateManyInput | GardeConfigCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeConfigIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * GardeConfig update
   */
  export type GardeConfigUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GardeConfig
     */
    select?: GardeConfigSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GardeConfig
     */
    omit?: GardeConfigOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeConfigInclude<ExtArgs> | null
    /**
     * The data needed to update a GardeConfig.
     */
    data: XOR<GardeConfigUpdateInput, GardeConfigUncheckedUpdateInput>
    /**
     * Choose, which GardeConfig to update.
     */
    where: GardeConfigWhereUniqueInput
  }

  /**
   * GardeConfig updateMany
   */
  export type GardeConfigUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update GardeConfigs.
     */
    data: XOR<GardeConfigUpdateManyMutationInput, GardeConfigUncheckedUpdateManyInput>
    /**
     * Filter which GardeConfigs to update
     */
    where?: GardeConfigWhereInput
    /**
     * Limit how many GardeConfigs to update.
     */
    limit?: number
  }

  /**
   * GardeConfig updateManyAndReturn
   */
  export type GardeConfigUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GardeConfig
     */
    select?: GardeConfigSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the GardeConfig
     */
    omit?: GardeConfigOmit<ExtArgs> | null
    /**
     * The data used to update GardeConfigs.
     */
    data: XOR<GardeConfigUpdateManyMutationInput, GardeConfigUncheckedUpdateManyInput>
    /**
     * Filter which GardeConfigs to update
     */
    where?: GardeConfigWhereInput
    /**
     * Limit how many GardeConfigs to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeConfigIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * GardeConfig upsert
   */
  export type GardeConfigUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GardeConfig
     */
    select?: GardeConfigSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GardeConfig
     */
    omit?: GardeConfigOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeConfigInclude<ExtArgs> | null
    /**
     * The filter to search for the GardeConfig to update in case it exists.
     */
    where: GardeConfigWhereUniqueInput
    /**
     * In case the GardeConfig found by the `where` argument doesn't exist, create a new GardeConfig with this data.
     */
    create: XOR<GardeConfigCreateInput, GardeConfigUncheckedCreateInput>
    /**
     * In case the GardeConfig was found with the provided `where` argument, update it with this data.
     */
    update: XOR<GardeConfigUpdateInput, GardeConfigUncheckedUpdateInput>
  }

  /**
   * GardeConfig delete
   */
  export type GardeConfigDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GardeConfig
     */
    select?: GardeConfigSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GardeConfig
     */
    omit?: GardeConfigOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeConfigInclude<ExtArgs> | null
    /**
     * Filter which GardeConfig to delete.
     */
    where: GardeConfigWhereUniqueInput
  }

  /**
   * GardeConfig deleteMany
   */
  export type GardeConfigDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which GardeConfigs to delete
     */
    where?: GardeConfigWhereInput
    /**
     * Limit how many GardeConfigs to delete.
     */
    limit?: number
  }

  /**
   * GardeConfig without action
   */
  export type GardeConfigDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GardeConfig
     */
    select?: GardeConfigSelect<ExtArgs> | null
    /**
     * Omit specific fields from the GardeConfig
     */
    omit?: GardeConfigOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeConfigInclude<ExtArgs> | null
  }


  /**
   * Model Garde
   */

  export type AggregateGarde = {
    _count: GardeCountAggregateOutputType | null
    _avg: GardeAvgAggregateOutputType | null
    _sum: GardeSumAggregateOutputType | null
    _min: GardeMinAggregateOutputType | null
    _max: GardeMaxAggregateOutputType | null
  }

  export type GardeAvgAggregateOutputType = {
    id: number | null
    medecinAssistantId: number | null
    medecinResidantId: number | null
    annee: number | null
  }

  export type GardeSumAggregateOutputType = {
    id: number | null
    medecinAssistantId: number | null
    medecinResidantId: number | null
    annee: number | null
  }

  export type GardeMinAggregateOutputType = {
    id: number | null
    date: string | null
    medecinAssistantId: number | null
    medecinResidantId: number | null
    mois: string | null
    annee: number | null
    createdAt: Date | null
  }

  export type GardeMaxAggregateOutputType = {
    id: number | null
    date: string | null
    medecinAssistantId: number | null
    medecinResidantId: number | null
    mois: string | null
    annee: number | null
    createdAt: Date | null
  }

  export type GardeCountAggregateOutputType = {
    id: number
    date: number
    medecinAssistantId: number
    medecinResidantId: number
    mois: number
    annee: number
    createdAt: number
    _all: number
  }


  export type GardeAvgAggregateInputType = {
    id?: true
    medecinAssistantId?: true
    medecinResidantId?: true
    annee?: true
  }

  export type GardeSumAggregateInputType = {
    id?: true
    medecinAssistantId?: true
    medecinResidantId?: true
    annee?: true
  }

  export type GardeMinAggregateInputType = {
    id?: true
    date?: true
    medecinAssistantId?: true
    medecinResidantId?: true
    mois?: true
    annee?: true
    createdAt?: true
  }

  export type GardeMaxAggregateInputType = {
    id?: true
    date?: true
    medecinAssistantId?: true
    medecinResidantId?: true
    mois?: true
    annee?: true
    createdAt?: true
  }

  export type GardeCountAggregateInputType = {
    id?: true
    date?: true
    medecinAssistantId?: true
    medecinResidantId?: true
    mois?: true
    annee?: true
    createdAt?: true
    _all?: true
  }

  export type GardeAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Garde to aggregate.
     */
    where?: GardeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Gardes to fetch.
     */
    orderBy?: GardeOrderByWithRelationInput | GardeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: GardeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Gardes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Gardes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Gardes
    **/
    _count?: true | GardeCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: GardeAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: GardeSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: GardeMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: GardeMaxAggregateInputType
  }

  export type GetGardeAggregateType<T extends GardeAggregateArgs> = {
        [P in keyof T & keyof AggregateGarde]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateGarde[P]>
      : GetScalarType<T[P], AggregateGarde[P]>
  }




  export type GardeGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: GardeWhereInput
    orderBy?: GardeOrderByWithAggregationInput | GardeOrderByWithAggregationInput[]
    by: GardeScalarFieldEnum[] | GardeScalarFieldEnum
    having?: GardeScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: GardeCountAggregateInputType | true
    _avg?: GardeAvgAggregateInputType
    _sum?: GardeSumAggregateInputType
    _min?: GardeMinAggregateInputType
    _max?: GardeMaxAggregateInputType
  }

  export type GardeGroupByOutputType = {
    id: number
    date: string
    medecinAssistantId: number | null
    medecinResidantId: number | null
    mois: string
    annee: number
    createdAt: Date
    _count: GardeCountAggregateOutputType | null
    _avg: GardeAvgAggregateOutputType | null
    _sum: GardeSumAggregateOutputType | null
    _min: GardeMinAggregateOutputType | null
    _max: GardeMaxAggregateOutputType | null
  }

  type GetGardeGroupByPayload<T extends GardeGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<GardeGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof GardeGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], GardeGroupByOutputType[P]>
            : GetScalarType<T[P], GardeGroupByOutputType[P]>
        }
      >
    >


  export type GardeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    date?: boolean
    medecinAssistantId?: boolean
    medecinResidantId?: boolean
    mois?: boolean
    annee?: boolean
    createdAt?: boolean
    medecinAssistant?: boolean | Garde$medecinAssistantArgs<ExtArgs>
    medecinResidant?: boolean | Garde$medecinResidantArgs<ExtArgs>
  }, ExtArgs["result"]["garde"]>

  export type GardeSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    date?: boolean
    medecinAssistantId?: boolean
    medecinResidantId?: boolean
    mois?: boolean
    annee?: boolean
    createdAt?: boolean
    medecinAssistant?: boolean | Garde$medecinAssistantArgs<ExtArgs>
    medecinResidant?: boolean | Garde$medecinResidantArgs<ExtArgs>
  }, ExtArgs["result"]["garde"]>

  export type GardeSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    date?: boolean
    medecinAssistantId?: boolean
    medecinResidantId?: boolean
    mois?: boolean
    annee?: boolean
    createdAt?: boolean
    medecinAssistant?: boolean | Garde$medecinAssistantArgs<ExtArgs>
    medecinResidant?: boolean | Garde$medecinResidantArgs<ExtArgs>
  }, ExtArgs["result"]["garde"]>

  export type GardeSelectScalar = {
    id?: boolean
    date?: boolean
    medecinAssistantId?: boolean
    medecinResidantId?: boolean
    mois?: boolean
    annee?: boolean
    createdAt?: boolean
  }

  export type GardeOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "date" | "medecinAssistantId" | "medecinResidantId" | "mois" | "annee" | "createdAt", ExtArgs["result"]["garde"]>
  export type GardeInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    medecinAssistant?: boolean | Garde$medecinAssistantArgs<ExtArgs>
    medecinResidant?: boolean | Garde$medecinResidantArgs<ExtArgs>
  }
  export type GardeIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    medecinAssistant?: boolean | Garde$medecinAssistantArgs<ExtArgs>
    medecinResidant?: boolean | Garde$medecinResidantArgs<ExtArgs>
  }
  export type GardeIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    medecinAssistant?: boolean | Garde$medecinAssistantArgs<ExtArgs>
    medecinResidant?: boolean | Garde$medecinResidantArgs<ExtArgs>
  }

  export type $GardePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Garde"
    objects: {
      medecinAssistant: Prisma.$MedecinPayload<ExtArgs> | null
      medecinResidant: Prisma.$MedecinPayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      date: string
      medecinAssistantId: number | null
      medecinResidantId: number | null
      mois: string
      annee: number
      createdAt: Date
    }, ExtArgs["result"]["garde"]>
    composites: {}
  }

  type GardeGetPayload<S extends boolean | null | undefined | GardeDefaultArgs> = $Result.GetResult<Prisma.$GardePayload, S>

  type GardeCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<GardeFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: GardeCountAggregateInputType | true
    }

  export interface GardeDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Garde'], meta: { name: 'Garde' } }
    /**
     * Find zero or one Garde that matches the filter.
     * @param {GardeFindUniqueArgs} args - Arguments to find a Garde
     * @example
     * // Get one Garde
     * const garde = await prisma.garde.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends GardeFindUniqueArgs>(args: SelectSubset<T, GardeFindUniqueArgs<ExtArgs>>): Prisma__GardeClient<$Result.GetResult<Prisma.$GardePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Garde that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {GardeFindUniqueOrThrowArgs} args - Arguments to find a Garde
     * @example
     * // Get one Garde
     * const garde = await prisma.garde.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends GardeFindUniqueOrThrowArgs>(args: SelectSubset<T, GardeFindUniqueOrThrowArgs<ExtArgs>>): Prisma__GardeClient<$Result.GetResult<Prisma.$GardePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Garde that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeFindFirstArgs} args - Arguments to find a Garde
     * @example
     * // Get one Garde
     * const garde = await prisma.garde.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends GardeFindFirstArgs>(args?: SelectSubset<T, GardeFindFirstArgs<ExtArgs>>): Prisma__GardeClient<$Result.GetResult<Prisma.$GardePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Garde that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeFindFirstOrThrowArgs} args - Arguments to find a Garde
     * @example
     * // Get one Garde
     * const garde = await prisma.garde.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends GardeFindFirstOrThrowArgs>(args?: SelectSubset<T, GardeFindFirstOrThrowArgs<ExtArgs>>): Prisma__GardeClient<$Result.GetResult<Prisma.$GardePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Gardes that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Gardes
     * const gardes = await prisma.garde.findMany()
     * 
     * // Get first 10 Gardes
     * const gardes = await prisma.garde.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const gardeWithIdOnly = await prisma.garde.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends GardeFindManyArgs>(args?: SelectSubset<T, GardeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GardePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Garde.
     * @param {GardeCreateArgs} args - Arguments to create a Garde.
     * @example
     * // Create one Garde
     * const Garde = await prisma.garde.create({
     *   data: {
     *     // ... data to create a Garde
     *   }
     * })
     * 
     */
    create<T extends GardeCreateArgs>(args: SelectSubset<T, GardeCreateArgs<ExtArgs>>): Prisma__GardeClient<$Result.GetResult<Prisma.$GardePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Gardes.
     * @param {GardeCreateManyArgs} args - Arguments to create many Gardes.
     * @example
     * // Create many Gardes
     * const garde = await prisma.garde.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends GardeCreateManyArgs>(args?: SelectSubset<T, GardeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Gardes and returns the data saved in the database.
     * @param {GardeCreateManyAndReturnArgs} args - Arguments to create many Gardes.
     * @example
     * // Create many Gardes
     * const garde = await prisma.garde.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Gardes and only return the `id`
     * const gardeWithIdOnly = await prisma.garde.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends GardeCreateManyAndReturnArgs>(args?: SelectSubset<T, GardeCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GardePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Garde.
     * @param {GardeDeleteArgs} args - Arguments to delete one Garde.
     * @example
     * // Delete one Garde
     * const Garde = await prisma.garde.delete({
     *   where: {
     *     // ... filter to delete one Garde
     *   }
     * })
     * 
     */
    delete<T extends GardeDeleteArgs>(args: SelectSubset<T, GardeDeleteArgs<ExtArgs>>): Prisma__GardeClient<$Result.GetResult<Prisma.$GardePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Garde.
     * @param {GardeUpdateArgs} args - Arguments to update one Garde.
     * @example
     * // Update one Garde
     * const garde = await prisma.garde.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends GardeUpdateArgs>(args: SelectSubset<T, GardeUpdateArgs<ExtArgs>>): Prisma__GardeClient<$Result.GetResult<Prisma.$GardePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Gardes.
     * @param {GardeDeleteManyArgs} args - Arguments to filter Gardes to delete.
     * @example
     * // Delete a few Gardes
     * const { count } = await prisma.garde.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends GardeDeleteManyArgs>(args?: SelectSubset<T, GardeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Gardes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Gardes
     * const garde = await prisma.garde.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends GardeUpdateManyArgs>(args: SelectSubset<T, GardeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Gardes and returns the data updated in the database.
     * @param {GardeUpdateManyAndReturnArgs} args - Arguments to update many Gardes.
     * @example
     * // Update many Gardes
     * const garde = await prisma.garde.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Gardes and only return the `id`
     * const gardeWithIdOnly = await prisma.garde.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends GardeUpdateManyAndReturnArgs>(args: SelectSubset<T, GardeUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GardePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Garde.
     * @param {GardeUpsertArgs} args - Arguments to update or create a Garde.
     * @example
     * // Update or create a Garde
     * const garde = await prisma.garde.upsert({
     *   create: {
     *     // ... data to create a Garde
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Garde we want to update
     *   }
     * })
     */
    upsert<T extends GardeUpsertArgs>(args: SelectSubset<T, GardeUpsertArgs<ExtArgs>>): Prisma__GardeClient<$Result.GetResult<Prisma.$GardePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Gardes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeCountArgs} args - Arguments to filter Gardes to count.
     * @example
     * // Count the number of Gardes
     * const count = await prisma.garde.count({
     *   where: {
     *     // ... the filter for the Gardes we want to count
     *   }
     * })
    **/
    count<T extends GardeCountArgs>(
      args?: Subset<T, GardeCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], GardeCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Garde.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends GardeAggregateArgs>(args: Subset<T, GardeAggregateArgs>): Prisma.PrismaPromise<GetGardeAggregateType<T>>

    /**
     * Group by Garde.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GardeGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends GardeGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: GardeGroupByArgs['orderBy'] }
        : { orderBy?: GardeGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, GardeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetGardeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Garde model
   */
  readonly fields: GardeFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Garde.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__GardeClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    medecinAssistant<T extends Garde$medecinAssistantArgs<ExtArgs> = {}>(args?: Subset<T, Garde$medecinAssistantArgs<ExtArgs>>): Prisma__MedecinClient<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
    medecinResidant<T extends Garde$medecinResidantArgs<ExtArgs> = {}>(args?: Subset<T, Garde$medecinResidantArgs<ExtArgs>>): Prisma__MedecinClient<$Result.GetResult<Prisma.$MedecinPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Garde model
   */
  interface GardeFieldRefs {
    readonly id: FieldRef<"Garde", 'Int'>
    readonly date: FieldRef<"Garde", 'String'>
    readonly medecinAssistantId: FieldRef<"Garde", 'Int'>
    readonly medecinResidantId: FieldRef<"Garde", 'Int'>
    readonly mois: FieldRef<"Garde", 'String'>
    readonly annee: FieldRef<"Garde", 'Int'>
    readonly createdAt: FieldRef<"Garde", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Garde findUnique
   */
  export type GardeFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeInclude<ExtArgs> | null
    /**
     * Filter, which Garde to fetch.
     */
    where: GardeWhereUniqueInput
  }

  /**
   * Garde findUniqueOrThrow
   */
  export type GardeFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeInclude<ExtArgs> | null
    /**
     * Filter, which Garde to fetch.
     */
    where: GardeWhereUniqueInput
  }

  /**
   * Garde findFirst
   */
  export type GardeFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeInclude<ExtArgs> | null
    /**
     * Filter, which Garde to fetch.
     */
    where?: GardeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Gardes to fetch.
     */
    orderBy?: GardeOrderByWithRelationInput | GardeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Gardes.
     */
    cursor?: GardeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Gardes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Gardes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Gardes.
     */
    distinct?: GardeScalarFieldEnum | GardeScalarFieldEnum[]
  }

  /**
   * Garde findFirstOrThrow
   */
  export type GardeFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeInclude<ExtArgs> | null
    /**
     * Filter, which Garde to fetch.
     */
    where?: GardeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Gardes to fetch.
     */
    orderBy?: GardeOrderByWithRelationInput | GardeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Gardes.
     */
    cursor?: GardeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Gardes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Gardes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Gardes.
     */
    distinct?: GardeScalarFieldEnum | GardeScalarFieldEnum[]
  }

  /**
   * Garde findMany
   */
  export type GardeFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeInclude<ExtArgs> | null
    /**
     * Filter, which Gardes to fetch.
     */
    where?: GardeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Gardes to fetch.
     */
    orderBy?: GardeOrderByWithRelationInput | GardeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Gardes.
     */
    cursor?: GardeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Gardes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Gardes.
     */
    skip?: number
    distinct?: GardeScalarFieldEnum | GardeScalarFieldEnum[]
  }

  /**
   * Garde create
   */
  export type GardeCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeInclude<ExtArgs> | null
    /**
     * The data needed to create a Garde.
     */
    data: XOR<GardeCreateInput, GardeUncheckedCreateInput>
  }

  /**
   * Garde createMany
   */
  export type GardeCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Gardes.
     */
    data: GardeCreateManyInput | GardeCreateManyInput[]
  }

  /**
   * Garde createManyAndReturn
   */
  export type GardeCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * The data used to create many Gardes.
     */
    data: GardeCreateManyInput | GardeCreateManyInput[]
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Garde update
   */
  export type GardeUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeInclude<ExtArgs> | null
    /**
     * The data needed to update a Garde.
     */
    data: XOR<GardeUpdateInput, GardeUncheckedUpdateInput>
    /**
     * Choose, which Garde to update.
     */
    where: GardeWhereUniqueInput
  }

  /**
   * Garde updateMany
   */
  export type GardeUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Gardes.
     */
    data: XOR<GardeUpdateManyMutationInput, GardeUncheckedUpdateManyInput>
    /**
     * Filter which Gardes to update
     */
    where?: GardeWhereInput
    /**
     * Limit how many Gardes to update.
     */
    limit?: number
  }

  /**
   * Garde updateManyAndReturn
   */
  export type GardeUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * The data used to update Gardes.
     */
    data: XOR<GardeUpdateManyMutationInput, GardeUncheckedUpdateManyInput>
    /**
     * Filter which Gardes to update
     */
    where?: GardeWhereInput
    /**
     * Limit how many Gardes to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * Garde upsert
   */
  export type GardeUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeInclude<ExtArgs> | null
    /**
     * The filter to search for the Garde to update in case it exists.
     */
    where: GardeWhereUniqueInput
    /**
     * In case the Garde found by the `where` argument doesn't exist, create a new Garde with this data.
     */
    create: XOR<GardeCreateInput, GardeUncheckedCreateInput>
    /**
     * In case the Garde was found with the provided `where` argument, update it with this data.
     */
    update: XOR<GardeUpdateInput, GardeUncheckedUpdateInput>
  }

  /**
   * Garde delete
   */
  export type GardeDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeInclude<ExtArgs> | null
    /**
     * Filter which Garde to delete.
     */
    where: GardeWhereUniqueInput
  }

  /**
   * Garde deleteMany
   */
  export type GardeDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Gardes to delete
     */
    where?: GardeWhereInput
    /**
     * Limit how many Gardes to delete.
     */
    limit?: number
  }

  /**
   * Garde.medecinAssistant
   */
  export type Garde$medecinAssistantArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MedecinInclude<ExtArgs> | null
    where?: MedecinWhereInput
  }

  /**
   * Garde.medecinResidant
   */
  export type Garde$medecinResidantArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Medecin
     */
    select?: MedecinSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Medecin
     */
    omit?: MedecinOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: MedecinInclude<ExtArgs> | null
    where?: MedecinWhereInput
  }

  /**
   * Garde without action
   */
  export type GardeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Garde
     */
    select?: GardeSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Garde
     */
    omit?: GardeOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GardeInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const MedecinScalarFieldEnum: {
    id: 'id',
    nom: 'nom',
    prenom: 'prenom',
    type: 'type',
    createdAt: 'createdAt'
  };

  export type MedecinScalarFieldEnum = (typeof MedecinScalarFieldEnum)[keyof typeof MedecinScalarFieldEnum]


  export const GardeConfigScalarFieldEnum: {
    id: 'id',
    medecinId: 'medecinId',
    nombreGardes: 'nombreGardes',
    mois: 'mois',
    annee: 'annee'
  };

  export type GardeConfigScalarFieldEnum = (typeof GardeConfigScalarFieldEnum)[keyof typeof GardeConfigScalarFieldEnum]


  export const GardeScalarFieldEnum: {
    id: 'id',
    date: 'date',
    medecinAssistantId: 'medecinAssistantId',
    medecinResidantId: 'medecinResidantId',
    mois: 'mois',
    annee: 'annee',
    createdAt: 'createdAt'
  };

  export type GardeScalarFieldEnum = (typeof GardeScalarFieldEnum)[keyof typeof GardeScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'TypeMedecin'
   */
  export type EnumTypeMedecinFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'TypeMedecin'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    
  /**
   * Deep Input Types
   */


  export type MedecinWhereInput = {
    AND?: MedecinWhereInput | MedecinWhereInput[]
    OR?: MedecinWhereInput[]
    NOT?: MedecinWhereInput | MedecinWhereInput[]
    id?: IntFilter<"Medecin"> | number
    nom?: StringFilter<"Medecin"> | string
    prenom?: StringFilter<"Medecin"> | string
    type?: EnumTypeMedecinFilter<"Medecin"> | $Enums.TypeMedecin
    createdAt?: DateTimeFilter<"Medecin"> | Date | string
    gardesAssistant?: GardeConfigListRelationFilter
    gardesEnTantQueAssistant?: GardeListRelationFilter
    gardesEnTantQueResidant?: GardeListRelationFilter
  }

  export type MedecinOrderByWithRelationInput = {
    id?: SortOrder
    nom?: SortOrder
    prenom?: SortOrder
    type?: SortOrder
    createdAt?: SortOrder
    gardesAssistant?: GardeConfigOrderByRelationAggregateInput
    gardesEnTantQueAssistant?: GardeOrderByRelationAggregateInput
    gardesEnTantQueResidant?: GardeOrderByRelationAggregateInput
  }

  export type MedecinWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    AND?: MedecinWhereInput | MedecinWhereInput[]
    OR?: MedecinWhereInput[]
    NOT?: MedecinWhereInput | MedecinWhereInput[]
    nom?: StringFilter<"Medecin"> | string
    prenom?: StringFilter<"Medecin"> | string
    type?: EnumTypeMedecinFilter<"Medecin"> | $Enums.TypeMedecin
    createdAt?: DateTimeFilter<"Medecin"> | Date | string
    gardesAssistant?: GardeConfigListRelationFilter
    gardesEnTantQueAssistant?: GardeListRelationFilter
    gardesEnTantQueResidant?: GardeListRelationFilter
  }, "id">

  export type MedecinOrderByWithAggregationInput = {
    id?: SortOrder
    nom?: SortOrder
    prenom?: SortOrder
    type?: SortOrder
    createdAt?: SortOrder
    _count?: MedecinCountOrderByAggregateInput
    _avg?: MedecinAvgOrderByAggregateInput
    _max?: MedecinMaxOrderByAggregateInput
    _min?: MedecinMinOrderByAggregateInput
    _sum?: MedecinSumOrderByAggregateInput
  }

  export type MedecinScalarWhereWithAggregatesInput = {
    AND?: MedecinScalarWhereWithAggregatesInput | MedecinScalarWhereWithAggregatesInput[]
    OR?: MedecinScalarWhereWithAggregatesInput[]
    NOT?: MedecinScalarWhereWithAggregatesInput | MedecinScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"Medecin"> | number
    nom?: StringWithAggregatesFilter<"Medecin"> | string
    prenom?: StringWithAggregatesFilter<"Medecin"> | string
    type?: EnumTypeMedecinWithAggregatesFilter<"Medecin"> | $Enums.TypeMedecin
    createdAt?: DateTimeWithAggregatesFilter<"Medecin"> | Date | string
  }

  export type GardeConfigWhereInput = {
    AND?: GardeConfigWhereInput | GardeConfigWhereInput[]
    OR?: GardeConfigWhereInput[]
    NOT?: GardeConfigWhereInput | GardeConfigWhereInput[]
    id?: IntFilter<"GardeConfig"> | number
    medecinId?: IntFilter<"GardeConfig"> | number
    nombreGardes?: IntFilter<"GardeConfig"> | number
    mois?: StringFilter<"GardeConfig"> | string
    annee?: IntFilter<"GardeConfig"> | number
    medecin?: XOR<MedecinScalarRelationFilter, MedecinWhereInput>
  }

  export type GardeConfigOrderByWithRelationInput = {
    id?: SortOrder
    medecinId?: SortOrder
    nombreGardes?: SortOrder
    mois?: SortOrder
    annee?: SortOrder
    medecin?: MedecinOrderByWithRelationInput
  }

  export type GardeConfigWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    medecinId_mois_annee?: GardeConfigMedecinIdMoisAnneeCompoundUniqueInput
    AND?: GardeConfigWhereInput | GardeConfigWhereInput[]
    OR?: GardeConfigWhereInput[]
    NOT?: GardeConfigWhereInput | GardeConfigWhereInput[]
    medecinId?: IntFilter<"GardeConfig"> | number
    nombreGardes?: IntFilter<"GardeConfig"> | number
    mois?: StringFilter<"GardeConfig"> | string
    annee?: IntFilter<"GardeConfig"> | number
    medecin?: XOR<MedecinScalarRelationFilter, MedecinWhereInput>
  }, "id" | "medecinId_mois_annee">

  export type GardeConfigOrderByWithAggregationInput = {
    id?: SortOrder
    medecinId?: SortOrder
    nombreGardes?: SortOrder
    mois?: SortOrder
    annee?: SortOrder
    _count?: GardeConfigCountOrderByAggregateInput
    _avg?: GardeConfigAvgOrderByAggregateInput
    _max?: GardeConfigMaxOrderByAggregateInput
    _min?: GardeConfigMinOrderByAggregateInput
    _sum?: GardeConfigSumOrderByAggregateInput
  }

  export type GardeConfigScalarWhereWithAggregatesInput = {
    AND?: GardeConfigScalarWhereWithAggregatesInput | GardeConfigScalarWhereWithAggregatesInput[]
    OR?: GardeConfigScalarWhereWithAggregatesInput[]
    NOT?: GardeConfigScalarWhereWithAggregatesInput | GardeConfigScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"GardeConfig"> | number
    medecinId?: IntWithAggregatesFilter<"GardeConfig"> | number
    nombreGardes?: IntWithAggregatesFilter<"GardeConfig"> | number
    mois?: StringWithAggregatesFilter<"GardeConfig"> | string
    annee?: IntWithAggregatesFilter<"GardeConfig"> | number
  }

  export type GardeWhereInput = {
    AND?: GardeWhereInput | GardeWhereInput[]
    OR?: GardeWhereInput[]
    NOT?: GardeWhereInput | GardeWhereInput[]
    id?: IntFilter<"Garde"> | number
    date?: StringFilter<"Garde"> | string
    medecinAssistantId?: IntNullableFilter<"Garde"> | number | null
    medecinResidantId?: IntNullableFilter<"Garde"> | number | null
    mois?: StringFilter<"Garde"> | string
    annee?: IntFilter<"Garde"> | number
    createdAt?: DateTimeFilter<"Garde"> | Date | string
    medecinAssistant?: XOR<MedecinNullableScalarRelationFilter, MedecinWhereInput> | null
    medecinResidant?: XOR<MedecinNullableScalarRelationFilter, MedecinWhereInput> | null
  }

  export type GardeOrderByWithRelationInput = {
    id?: SortOrder
    date?: SortOrder
    medecinAssistantId?: SortOrderInput | SortOrder
    medecinResidantId?: SortOrderInput | SortOrder
    mois?: SortOrder
    annee?: SortOrder
    createdAt?: SortOrder
    medecinAssistant?: MedecinOrderByWithRelationInput
    medecinResidant?: MedecinOrderByWithRelationInput
  }

  export type GardeWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    AND?: GardeWhereInput | GardeWhereInput[]
    OR?: GardeWhereInput[]
    NOT?: GardeWhereInput | GardeWhereInput[]
    date?: StringFilter<"Garde"> | string
    medecinAssistantId?: IntNullableFilter<"Garde"> | number | null
    medecinResidantId?: IntNullableFilter<"Garde"> | number | null
    mois?: StringFilter<"Garde"> | string
    annee?: IntFilter<"Garde"> | number
    createdAt?: DateTimeFilter<"Garde"> | Date | string
    medecinAssistant?: XOR<MedecinNullableScalarRelationFilter, MedecinWhereInput> | null
    medecinResidant?: XOR<MedecinNullableScalarRelationFilter, MedecinWhereInput> | null
  }, "id">

  export type GardeOrderByWithAggregationInput = {
    id?: SortOrder
    date?: SortOrder
    medecinAssistantId?: SortOrderInput | SortOrder
    medecinResidantId?: SortOrderInput | SortOrder
    mois?: SortOrder
    annee?: SortOrder
    createdAt?: SortOrder
    _count?: GardeCountOrderByAggregateInput
    _avg?: GardeAvgOrderByAggregateInput
    _max?: GardeMaxOrderByAggregateInput
    _min?: GardeMinOrderByAggregateInput
    _sum?: GardeSumOrderByAggregateInput
  }

  export type GardeScalarWhereWithAggregatesInput = {
    AND?: GardeScalarWhereWithAggregatesInput | GardeScalarWhereWithAggregatesInput[]
    OR?: GardeScalarWhereWithAggregatesInput[]
    NOT?: GardeScalarWhereWithAggregatesInput | GardeScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"Garde"> | number
    date?: StringWithAggregatesFilter<"Garde"> | string
    medecinAssistantId?: IntNullableWithAggregatesFilter<"Garde"> | number | null
    medecinResidantId?: IntNullableWithAggregatesFilter<"Garde"> | number | null
    mois?: StringWithAggregatesFilter<"Garde"> | string
    annee?: IntWithAggregatesFilter<"Garde"> | number
    createdAt?: DateTimeWithAggregatesFilter<"Garde"> | Date | string
  }

  export type MedecinCreateInput = {
    nom: string
    prenom: string
    type: $Enums.TypeMedecin
    createdAt?: Date | string
    gardesAssistant?: GardeConfigCreateNestedManyWithoutMedecinInput
    gardesEnTantQueAssistant?: GardeCreateNestedManyWithoutMedecinAssistantInput
    gardesEnTantQueResidant?: GardeCreateNestedManyWithoutMedecinResidantInput
  }

  export type MedecinUncheckedCreateInput = {
    id?: number
    nom: string
    prenom: string
    type: $Enums.TypeMedecin
    createdAt?: Date | string
    gardesAssistant?: GardeConfigUncheckedCreateNestedManyWithoutMedecinInput
    gardesEnTantQueAssistant?: GardeUncheckedCreateNestedManyWithoutMedecinAssistantInput
    gardesEnTantQueResidant?: GardeUncheckedCreateNestedManyWithoutMedecinResidantInput
  }

  export type MedecinUpdateInput = {
    nom?: StringFieldUpdateOperationsInput | string
    prenom?: StringFieldUpdateOperationsInput | string
    type?: EnumTypeMedecinFieldUpdateOperationsInput | $Enums.TypeMedecin
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    gardesAssistant?: GardeConfigUpdateManyWithoutMedecinNestedInput
    gardesEnTantQueAssistant?: GardeUpdateManyWithoutMedecinAssistantNestedInput
    gardesEnTantQueResidant?: GardeUpdateManyWithoutMedecinResidantNestedInput
  }

  export type MedecinUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    nom?: StringFieldUpdateOperationsInput | string
    prenom?: StringFieldUpdateOperationsInput | string
    type?: EnumTypeMedecinFieldUpdateOperationsInput | $Enums.TypeMedecin
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    gardesAssistant?: GardeConfigUncheckedUpdateManyWithoutMedecinNestedInput
    gardesEnTantQueAssistant?: GardeUncheckedUpdateManyWithoutMedecinAssistantNestedInput
    gardesEnTantQueResidant?: GardeUncheckedUpdateManyWithoutMedecinResidantNestedInput
  }

  export type MedecinCreateManyInput = {
    id?: number
    nom: string
    prenom: string
    type: $Enums.TypeMedecin
    createdAt?: Date | string
  }

  export type MedecinUpdateManyMutationInput = {
    nom?: StringFieldUpdateOperationsInput | string
    prenom?: StringFieldUpdateOperationsInput | string
    type?: EnumTypeMedecinFieldUpdateOperationsInput | $Enums.TypeMedecin
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type MedecinUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    nom?: StringFieldUpdateOperationsInput | string
    prenom?: StringFieldUpdateOperationsInput | string
    type?: EnumTypeMedecinFieldUpdateOperationsInput | $Enums.TypeMedecin
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GardeConfigCreateInput = {
    nombreGardes: number
    mois: string
    annee: number
    medecin: MedecinCreateNestedOneWithoutGardesAssistantInput
  }

  export type GardeConfigUncheckedCreateInput = {
    id?: number
    medecinId: number
    nombreGardes: number
    mois: string
    annee: number
  }

  export type GardeConfigUpdateInput = {
    nombreGardes?: IntFieldUpdateOperationsInput | number
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
    medecin?: MedecinUpdateOneRequiredWithoutGardesAssistantNestedInput
  }

  export type GardeConfigUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    medecinId?: IntFieldUpdateOperationsInput | number
    nombreGardes?: IntFieldUpdateOperationsInput | number
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
  }

  export type GardeConfigCreateManyInput = {
    id?: number
    medecinId: number
    nombreGardes: number
    mois: string
    annee: number
  }

  export type GardeConfigUpdateManyMutationInput = {
    nombreGardes?: IntFieldUpdateOperationsInput | number
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
  }

  export type GardeConfigUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    medecinId?: IntFieldUpdateOperationsInput | number
    nombreGardes?: IntFieldUpdateOperationsInput | number
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
  }

  export type GardeCreateInput = {
    date: string
    mois: string
    annee: number
    createdAt?: Date | string
    medecinAssistant?: MedecinCreateNestedOneWithoutGardesEnTantQueAssistantInput
    medecinResidant?: MedecinCreateNestedOneWithoutGardesEnTantQueResidantInput
  }

  export type GardeUncheckedCreateInput = {
    id?: number
    date: string
    medecinAssistantId?: number | null
    medecinResidantId?: number | null
    mois: string
    annee: number
    createdAt?: Date | string
  }

  export type GardeUpdateInput = {
    date?: StringFieldUpdateOperationsInput | string
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    medecinAssistant?: MedecinUpdateOneWithoutGardesEnTantQueAssistantNestedInput
    medecinResidant?: MedecinUpdateOneWithoutGardesEnTantQueResidantNestedInput
  }

  export type GardeUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    date?: StringFieldUpdateOperationsInput | string
    medecinAssistantId?: NullableIntFieldUpdateOperationsInput | number | null
    medecinResidantId?: NullableIntFieldUpdateOperationsInput | number | null
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GardeCreateManyInput = {
    id?: number
    date: string
    medecinAssistantId?: number | null
    medecinResidantId?: number | null
    mois: string
    annee: number
    createdAt?: Date | string
  }

  export type GardeUpdateManyMutationInput = {
    date?: StringFieldUpdateOperationsInput | string
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GardeUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    date?: StringFieldUpdateOperationsInput | string
    medecinAssistantId?: NullableIntFieldUpdateOperationsInput | number | null
    medecinResidantId?: NullableIntFieldUpdateOperationsInput | number | null
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type EnumTypeMedecinFilter<$PrismaModel = never> = {
    equals?: $Enums.TypeMedecin | EnumTypeMedecinFieldRefInput<$PrismaModel>
    in?: $Enums.TypeMedecin[]
    notIn?: $Enums.TypeMedecin[]
    not?: NestedEnumTypeMedecinFilter<$PrismaModel> | $Enums.TypeMedecin
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type GardeConfigListRelationFilter = {
    every?: GardeConfigWhereInput
    some?: GardeConfigWhereInput
    none?: GardeConfigWhereInput
  }

  export type GardeListRelationFilter = {
    every?: GardeWhereInput
    some?: GardeWhereInput
    none?: GardeWhereInput
  }

  export type GardeConfigOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type GardeOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type MedecinCountOrderByAggregateInput = {
    id?: SortOrder
    nom?: SortOrder
    prenom?: SortOrder
    type?: SortOrder
    createdAt?: SortOrder
  }

  export type MedecinAvgOrderByAggregateInput = {
    id?: SortOrder
  }

  export type MedecinMaxOrderByAggregateInput = {
    id?: SortOrder
    nom?: SortOrder
    prenom?: SortOrder
    type?: SortOrder
    createdAt?: SortOrder
  }

  export type MedecinMinOrderByAggregateInput = {
    id?: SortOrder
    nom?: SortOrder
    prenom?: SortOrder
    type?: SortOrder
    createdAt?: SortOrder
  }

  export type MedecinSumOrderByAggregateInput = {
    id?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type EnumTypeMedecinWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.TypeMedecin | EnumTypeMedecinFieldRefInput<$PrismaModel>
    in?: $Enums.TypeMedecin[]
    notIn?: $Enums.TypeMedecin[]
    not?: NestedEnumTypeMedecinWithAggregatesFilter<$PrismaModel> | $Enums.TypeMedecin
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumTypeMedecinFilter<$PrismaModel>
    _max?: NestedEnumTypeMedecinFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type MedecinScalarRelationFilter = {
    is?: MedecinWhereInput
    isNot?: MedecinWhereInput
  }

  export type GardeConfigMedecinIdMoisAnneeCompoundUniqueInput = {
    medecinId: number
    mois: string
    annee: number
  }

  export type GardeConfigCountOrderByAggregateInput = {
    id?: SortOrder
    medecinId?: SortOrder
    nombreGardes?: SortOrder
    mois?: SortOrder
    annee?: SortOrder
  }

  export type GardeConfigAvgOrderByAggregateInput = {
    id?: SortOrder
    medecinId?: SortOrder
    nombreGardes?: SortOrder
    annee?: SortOrder
  }

  export type GardeConfigMaxOrderByAggregateInput = {
    id?: SortOrder
    medecinId?: SortOrder
    nombreGardes?: SortOrder
    mois?: SortOrder
    annee?: SortOrder
  }

  export type GardeConfigMinOrderByAggregateInput = {
    id?: SortOrder
    medecinId?: SortOrder
    nombreGardes?: SortOrder
    mois?: SortOrder
    annee?: SortOrder
  }

  export type GardeConfigSumOrderByAggregateInput = {
    id?: SortOrder
    medecinId?: SortOrder
    nombreGardes?: SortOrder
    annee?: SortOrder
  }

  export type IntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type MedecinNullableScalarRelationFilter = {
    is?: MedecinWhereInput | null
    isNot?: MedecinWhereInput | null
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type GardeCountOrderByAggregateInput = {
    id?: SortOrder
    date?: SortOrder
    medecinAssistantId?: SortOrder
    medecinResidantId?: SortOrder
    mois?: SortOrder
    annee?: SortOrder
    createdAt?: SortOrder
  }

  export type GardeAvgOrderByAggregateInput = {
    id?: SortOrder
    medecinAssistantId?: SortOrder
    medecinResidantId?: SortOrder
    annee?: SortOrder
  }

  export type GardeMaxOrderByAggregateInput = {
    id?: SortOrder
    date?: SortOrder
    medecinAssistantId?: SortOrder
    medecinResidantId?: SortOrder
    mois?: SortOrder
    annee?: SortOrder
    createdAt?: SortOrder
  }

  export type GardeMinOrderByAggregateInput = {
    id?: SortOrder
    date?: SortOrder
    medecinAssistantId?: SortOrder
    medecinResidantId?: SortOrder
    mois?: SortOrder
    annee?: SortOrder
    createdAt?: SortOrder
  }

  export type GardeSumOrderByAggregateInput = {
    id?: SortOrder
    medecinAssistantId?: SortOrder
    medecinResidantId?: SortOrder
    annee?: SortOrder
  }

  export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type GardeConfigCreateNestedManyWithoutMedecinInput = {
    create?: XOR<GardeConfigCreateWithoutMedecinInput, GardeConfigUncheckedCreateWithoutMedecinInput> | GardeConfigCreateWithoutMedecinInput[] | GardeConfigUncheckedCreateWithoutMedecinInput[]
    connectOrCreate?: GardeConfigCreateOrConnectWithoutMedecinInput | GardeConfigCreateOrConnectWithoutMedecinInput[]
    createMany?: GardeConfigCreateManyMedecinInputEnvelope
    connect?: GardeConfigWhereUniqueInput | GardeConfigWhereUniqueInput[]
  }

  export type GardeCreateNestedManyWithoutMedecinAssistantInput = {
    create?: XOR<GardeCreateWithoutMedecinAssistantInput, GardeUncheckedCreateWithoutMedecinAssistantInput> | GardeCreateWithoutMedecinAssistantInput[] | GardeUncheckedCreateWithoutMedecinAssistantInput[]
    connectOrCreate?: GardeCreateOrConnectWithoutMedecinAssistantInput | GardeCreateOrConnectWithoutMedecinAssistantInput[]
    createMany?: GardeCreateManyMedecinAssistantInputEnvelope
    connect?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
  }

  export type GardeCreateNestedManyWithoutMedecinResidantInput = {
    create?: XOR<GardeCreateWithoutMedecinResidantInput, GardeUncheckedCreateWithoutMedecinResidantInput> | GardeCreateWithoutMedecinResidantInput[] | GardeUncheckedCreateWithoutMedecinResidantInput[]
    connectOrCreate?: GardeCreateOrConnectWithoutMedecinResidantInput | GardeCreateOrConnectWithoutMedecinResidantInput[]
    createMany?: GardeCreateManyMedecinResidantInputEnvelope
    connect?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
  }

  export type GardeConfigUncheckedCreateNestedManyWithoutMedecinInput = {
    create?: XOR<GardeConfigCreateWithoutMedecinInput, GardeConfigUncheckedCreateWithoutMedecinInput> | GardeConfigCreateWithoutMedecinInput[] | GardeConfigUncheckedCreateWithoutMedecinInput[]
    connectOrCreate?: GardeConfigCreateOrConnectWithoutMedecinInput | GardeConfigCreateOrConnectWithoutMedecinInput[]
    createMany?: GardeConfigCreateManyMedecinInputEnvelope
    connect?: GardeConfigWhereUniqueInput | GardeConfigWhereUniqueInput[]
  }

  export type GardeUncheckedCreateNestedManyWithoutMedecinAssistantInput = {
    create?: XOR<GardeCreateWithoutMedecinAssistantInput, GardeUncheckedCreateWithoutMedecinAssistantInput> | GardeCreateWithoutMedecinAssistantInput[] | GardeUncheckedCreateWithoutMedecinAssistantInput[]
    connectOrCreate?: GardeCreateOrConnectWithoutMedecinAssistantInput | GardeCreateOrConnectWithoutMedecinAssistantInput[]
    createMany?: GardeCreateManyMedecinAssistantInputEnvelope
    connect?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
  }

  export type GardeUncheckedCreateNestedManyWithoutMedecinResidantInput = {
    create?: XOR<GardeCreateWithoutMedecinResidantInput, GardeUncheckedCreateWithoutMedecinResidantInput> | GardeCreateWithoutMedecinResidantInput[] | GardeUncheckedCreateWithoutMedecinResidantInput[]
    connectOrCreate?: GardeCreateOrConnectWithoutMedecinResidantInput | GardeCreateOrConnectWithoutMedecinResidantInput[]
    createMany?: GardeCreateManyMedecinResidantInputEnvelope
    connect?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type EnumTypeMedecinFieldUpdateOperationsInput = {
    set?: $Enums.TypeMedecin
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type GardeConfigUpdateManyWithoutMedecinNestedInput = {
    create?: XOR<GardeConfigCreateWithoutMedecinInput, GardeConfigUncheckedCreateWithoutMedecinInput> | GardeConfigCreateWithoutMedecinInput[] | GardeConfigUncheckedCreateWithoutMedecinInput[]
    connectOrCreate?: GardeConfigCreateOrConnectWithoutMedecinInput | GardeConfigCreateOrConnectWithoutMedecinInput[]
    upsert?: GardeConfigUpsertWithWhereUniqueWithoutMedecinInput | GardeConfigUpsertWithWhereUniqueWithoutMedecinInput[]
    createMany?: GardeConfigCreateManyMedecinInputEnvelope
    set?: GardeConfigWhereUniqueInput | GardeConfigWhereUniqueInput[]
    disconnect?: GardeConfigWhereUniqueInput | GardeConfigWhereUniqueInput[]
    delete?: GardeConfigWhereUniqueInput | GardeConfigWhereUniqueInput[]
    connect?: GardeConfigWhereUniqueInput | GardeConfigWhereUniqueInput[]
    update?: GardeConfigUpdateWithWhereUniqueWithoutMedecinInput | GardeConfigUpdateWithWhereUniqueWithoutMedecinInput[]
    updateMany?: GardeConfigUpdateManyWithWhereWithoutMedecinInput | GardeConfigUpdateManyWithWhereWithoutMedecinInput[]
    deleteMany?: GardeConfigScalarWhereInput | GardeConfigScalarWhereInput[]
  }

  export type GardeUpdateManyWithoutMedecinAssistantNestedInput = {
    create?: XOR<GardeCreateWithoutMedecinAssistantInput, GardeUncheckedCreateWithoutMedecinAssistantInput> | GardeCreateWithoutMedecinAssistantInput[] | GardeUncheckedCreateWithoutMedecinAssistantInput[]
    connectOrCreate?: GardeCreateOrConnectWithoutMedecinAssistantInput | GardeCreateOrConnectWithoutMedecinAssistantInput[]
    upsert?: GardeUpsertWithWhereUniqueWithoutMedecinAssistantInput | GardeUpsertWithWhereUniqueWithoutMedecinAssistantInput[]
    createMany?: GardeCreateManyMedecinAssistantInputEnvelope
    set?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    disconnect?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    delete?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    connect?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    update?: GardeUpdateWithWhereUniqueWithoutMedecinAssistantInput | GardeUpdateWithWhereUniqueWithoutMedecinAssistantInput[]
    updateMany?: GardeUpdateManyWithWhereWithoutMedecinAssistantInput | GardeUpdateManyWithWhereWithoutMedecinAssistantInput[]
    deleteMany?: GardeScalarWhereInput | GardeScalarWhereInput[]
  }

  export type GardeUpdateManyWithoutMedecinResidantNestedInput = {
    create?: XOR<GardeCreateWithoutMedecinResidantInput, GardeUncheckedCreateWithoutMedecinResidantInput> | GardeCreateWithoutMedecinResidantInput[] | GardeUncheckedCreateWithoutMedecinResidantInput[]
    connectOrCreate?: GardeCreateOrConnectWithoutMedecinResidantInput | GardeCreateOrConnectWithoutMedecinResidantInput[]
    upsert?: GardeUpsertWithWhereUniqueWithoutMedecinResidantInput | GardeUpsertWithWhereUniqueWithoutMedecinResidantInput[]
    createMany?: GardeCreateManyMedecinResidantInputEnvelope
    set?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    disconnect?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    delete?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    connect?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    update?: GardeUpdateWithWhereUniqueWithoutMedecinResidantInput | GardeUpdateWithWhereUniqueWithoutMedecinResidantInput[]
    updateMany?: GardeUpdateManyWithWhereWithoutMedecinResidantInput | GardeUpdateManyWithWhereWithoutMedecinResidantInput[]
    deleteMany?: GardeScalarWhereInput | GardeScalarWhereInput[]
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type GardeConfigUncheckedUpdateManyWithoutMedecinNestedInput = {
    create?: XOR<GardeConfigCreateWithoutMedecinInput, GardeConfigUncheckedCreateWithoutMedecinInput> | GardeConfigCreateWithoutMedecinInput[] | GardeConfigUncheckedCreateWithoutMedecinInput[]
    connectOrCreate?: GardeConfigCreateOrConnectWithoutMedecinInput | GardeConfigCreateOrConnectWithoutMedecinInput[]
    upsert?: GardeConfigUpsertWithWhereUniqueWithoutMedecinInput | GardeConfigUpsertWithWhereUniqueWithoutMedecinInput[]
    createMany?: GardeConfigCreateManyMedecinInputEnvelope
    set?: GardeConfigWhereUniqueInput | GardeConfigWhereUniqueInput[]
    disconnect?: GardeConfigWhereUniqueInput | GardeConfigWhereUniqueInput[]
    delete?: GardeConfigWhereUniqueInput | GardeConfigWhereUniqueInput[]
    connect?: GardeConfigWhereUniqueInput | GardeConfigWhereUniqueInput[]
    update?: GardeConfigUpdateWithWhereUniqueWithoutMedecinInput | GardeConfigUpdateWithWhereUniqueWithoutMedecinInput[]
    updateMany?: GardeConfigUpdateManyWithWhereWithoutMedecinInput | GardeConfigUpdateManyWithWhereWithoutMedecinInput[]
    deleteMany?: GardeConfigScalarWhereInput | GardeConfigScalarWhereInput[]
  }

  export type GardeUncheckedUpdateManyWithoutMedecinAssistantNestedInput = {
    create?: XOR<GardeCreateWithoutMedecinAssistantInput, GardeUncheckedCreateWithoutMedecinAssistantInput> | GardeCreateWithoutMedecinAssistantInput[] | GardeUncheckedCreateWithoutMedecinAssistantInput[]
    connectOrCreate?: GardeCreateOrConnectWithoutMedecinAssistantInput | GardeCreateOrConnectWithoutMedecinAssistantInput[]
    upsert?: GardeUpsertWithWhereUniqueWithoutMedecinAssistantInput | GardeUpsertWithWhereUniqueWithoutMedecinAssistantInput[]
    createMany?: GardeCreateManyMedecinAssistantInputEnvelope
    set?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    disconnect?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    delete?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    connect?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    update?: GardeUpdateWithWhereUniqueWithoutMedecinAssistantInput | GardeUpdateWithWhereUniqueWithoutMedecinAssistantInput[]
    updateMany?: GardeUpdateManyWithWhereWithoutMedecinAssistantInput | GardeUpdateManyWithWhereWithoutMedecinAssistantInput[]
    deleteMany?: GardeScalarWhereInput | GardeScalarWhereInput[]
  }

  export type GardeUncheckedUpdateManyWithoutMedecinResidantNestedInput = {
    create?: XOR<GardeCreateWithoutMedecinResidantInput, GardeUncheckedCreateWithoutMedecinResidantInput> | GardeCreateWithoutMedecinResidantInput[] | GardeUncheckedCreateWithoutMedecinResidantInput[]
    connectOrCreate?: GardeCreateOrConnectWithoutMedecinResidantInput | GardeCreateOrConnectWithoutMedecinResidantInput[]
    upsert?: GardeUpsertWithWhereUniqueWithoutMedecinResidantInput | GardeUpsertWithWhereUniqueWithoutMedecinResidantInput[]
    createMany?: GardeCreateManyMedecinResidantInputEnvelope
    set?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    disconnect?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    delete?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    connect?: GardeWhereUniqueInput | GardeWhereUniqueInput[]
    update?: GardeUpdateWithWhereUniqueWithoutMedecinResidantInput | GardeUpdateWithWhereUniqueWithoutMedecinResidantInput[]
    updateMany?: GardeUpdateManyWithWhereWithoutMedecinResidantInput | GardeUpdateManyWithWhereWithoutMedecinResidantInput[]
    deleteMany?: GardeScalarWhereInput | GardeScalarWhereInput[]
  }

  export type MedecinCreateNestedOneWithoutGardesAssistantInput = {
    create?: XOR<MedecinCreateWithoutGardesAssistantInput, MedecinUncheckedCreateWithoutGardesAssistantInput>
    connectOrCreate?: MedecinCreateOrConnectWithoutGardesAssistantInput
    connect?: MedecinWhereUniqueInput
  }

  export type MedecinUpdateOneRequiredWithoutGardesAssistantNestedInput = {
    create?: XOR<MedecinCreateWithoutGardesAssistantInput, MedecinUncheckedCreateWithoutGardesAssistantInput>
    connectOrCreate?: MedecinCreateOrConnectWithoutGardesAssistantInput
    upsert?: MedecinUpsertWithoutGardesAssistantInput
    connect?: MedecinWhereUniqueInput
    update?: XOR<XOR<MedecinUpdateToOneWithWhereWithoutGardesAssistantInput, MedecinUpdateWithoutGardesAssistantInput>, MedecinUncheckedUpdateWithoutGardesAssistantInput>
  }

  export type MedecinCreateNestedOneWithoutGardesEnTantQueAssistantInput = {
    create?: XOR<MedecinCreateWithoutGardesEnTantQueAssistantInput, MedecinUncheckedCreateWithoutGardesEnTantQueAssistantInput>
    connectOrCreate?: MedecinCreateOrConnectWithoutGardesEnTantQueAssistantInput
    connect?: MedecinWhereUniqueInput
  }

  export type MedecinCreateNestedOneWithoutGardesEnTantQueResidantInput = {
    create?: XOR<MedecinCreateWithoutGardesEnTantQueResidantInput, MedecinUncheckedCreateWithoutGardesEnTantQueResidantInput>
    connectOrCreate?: MedecinCreateOrConnectWithoutGardesEnTantQueResidantInput
    connect?: MedecinWhereUniqueInput
  }

  export type MedecinUpdateOneWithoutGardesEnTantQueAssistantNestedInput = {
    create?: XOR<MedecinCreateWithoutGardesEnTantQueAssistantInput, MedecinUncheckedCreateWithoutGardesEnTantQueAssistantInput>
    connectOrCreate?: MedecinCreateOrConnectWithoutGardesEnTantQueAssistantInput
    upsert?: MedecinUpsertWithoutGardesEnTantQueAssistantInput
    disconnect?: MedecinWhereInput | boolean
    delete?: MedecinWhereInput | boolean
    connect?: MedecinWhereUniqueInput
    update?: XOR<XOR<MedecinUpdateToOneWithWhereWithoutGardesEnTantQueAssistantInput, MedecinUpdateWithoutGardesEnTantQueAssistantInput>, MedecinUncheckedUpdateWithoutGardesEnTantQueAssistantInput>
  }

  export type MedecinUpdateOneWithoutGardesEnTantQueResidantNestedInput = {
    create?: XOR<MedecinCreateWithoutGardesEnTantQueResidantInput, MedecinUncheckedCreateWithoutGardesEnTantQueResidantInput>
    connectOrCreate?: MedecinCreateOrConnectWithoutGardesEnTantQueResidantInput
    upsert?: MedecinUpsertWithoutGardesEnTantQueResidantInput
    disconnect?: MedecinWhereInput | boolean
    delete?: MedecinWhereInput | boolean
    connect?: MedecinWhereUniqueInput
    update?: XOR<XOR<MedecinUpdateToOneWithWhereWithoutGardesEnTantQueResidantInput, MedecinUpdateWithoutGardesEnTantQueResidantInput>, MedecinUncheckedUpdateWithoutGardesEnTantQueResidantInput>
  }

  export type NullableIntFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedEnumTypeMedecinFilter<$PrismaModel = never> = {
    equals?: $Enums.TypeMedecin | EnumTypeMedecinFieldRefInput<$PrismaModel>
    in?: $Enums.TypeMedecin[]
    notIn?: $Enums.TypeMedecin[]
    not?: NestedEnumTypeMedecinFilter<$PrismaModel> | $Enums.TypeMedecin
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[]
    notIn?: number[]
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[]
    notIn?: string[]
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedEnumTypeMedecinWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.TypeMedecin | EnumTypeMedecinFieldRefInput<$PrismaModel>
    in?: $Enums.TypeMedecin[]
    notIn?: $Enums.TypeMedecin[]
    not?: NestedEnumTypeMedecinWithAggregatesFilter<$PrismaModel> | $Enums.TypeMedecin
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumTypeMedecinFilter<$PrismaModel>
    _max?: NestedEnumTypeMedecinFilter<$PrismaModel>
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[]
    notIn?: Date[] | string[]
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | null
    notIn?: number[] | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }

  export type GardeConfigCreateWithoutMedecinInput = {
    nombreGardes: number
    mois: string
    annee: number
  }

  export type GardeConfigUncheckedCreateWithoutMedecinInput = {
    id?: number
    nombreGardes: number
    mois: string
    annee: number
  }

  export type GardeConfigCreateOrConnectWithoutMedecinInput = {
    where: GardeConfigWhereUniqueInput
    create: XOR<GardeConfigCreateWithoutMedecinInput, GardeConfigUncheckedCreateWithoutMedecinInput>
  }

  export type GardeConfigCreateManyMedecinInputEnvelope = {
    data: GardeConfigCreateManyMedecinInput | GardeConfigCreateManyMedecinInput[]
  }

  export type GardeCreateWithoutMedecinAssistantInput = {
    date: string
    mois: string
    annee: number
    createdAt?: Date | string
    medecinResidant?: MedecinCreateNestedOneWithoutGardesEnTantQueResidantInput
  }

  export type GardeUncheckedCreateWithoutMedecinAssistantInput = {
    id?: number
    date: string
    medecinResidantId?: number | null
    mois: string
    annee: number
    createdAt?: Date | string
  }

  export type GardeCreateOrConnectWithoutMedecinAssistantInput = {
    where: GardeWhereUniqueInput
    create: XOR<GardeCreateWithoutMedecinAssistantInput, GardeUncheckedCreateWithoutMedecinAssistantInput>
  }

  export type GardeCreateManyMedecinAssistantInputEnvelope = {
    data: GardeCreateManyMedecinAssistantInput | GardeCreateManyMedecinAssistantInput[]
  }

  export type GardeCreateWithoutMedecinResidantInput = {
    date: string
    mois: string
    annee: number
    createdAt?: Date | string
    medecinAssistant?: MedecinCreateNestedOneWithoutGardesEnTantQueAssistantInput
  }

  export type GardeUncheckedCreateWithoutMedecinResidantInput = {
    id?: number
    date: string
    medecinAssistantId?: number | null
    mois: string
    annee: number
    createdAt?: Date | string
  }

  export type GardeCreateOrConnectWithoutMedecinResidantInput = {
    where: GardeWhereUniqueInput
    create: XOR<GardeCreateWithoutMedecinResidantInput, GardeUncheckedCreateWithoutMedecinResidantInput>
  }

  export type GardeCreateManyMedecinResidantInputEnvelope = {
    data: GardeCreateManyMedecinResidantInput | GardeCreateManyMedecinResidantInput[]
  }

  export type GardeConfigUpsertWithWhereUniqueWithoutMedecinInput = {
    where: GardeConfigWhereUniqueInput
    update: XOR<GardeConfigUpdateWithoutMedecinInput, GardeConfigUncheckedUpdateWithoutMedecinInput>
    create: XOR<GardeConfigCreateWithoutMedecinInput, GardeConfigUncheckedCreateWithoutMedecinInput>
  }

  export type GardeConfigUpdateWithWhereUniqueWithoutMedecinInput = {
    where: GardeConfigWhereUniqueInput
    data: XOR<GardeConfigUpdateWithoutMedecinInput, GardeConfigUncheckedUpdateWithoutMedecinInput>
  }

  export type GardeConfigUpdateManyWithWhereWithoutMedecinInput = {
    where: GardeConfigScalarWhereInput
    data: XOR<GardeConfigUpdateManyMutationInput, GardeConfigUncheckedUpdateManyWithoutMedecinInput>
  }

  export type GardeConfigScalarWhereInput = {
    AND?: GardeConfigScalarWhereInput | GardeConfigScalarWhereInput[]
    OR?: GardeConfigScalarWhereInput[]
    NOT?: GardeConfigScalarWhereInput | GardeConfigScalarWhereInput[]
    id?: IntFilter<"GardeConfig"> | number
    medecinId?: IntFilter<"GardeConfig"> | number
    nombreGardes?: IntFilter<"GardeConfig"> | number
    mois?: StringFilter<"GardeConfig"> | string
    annee?: IntFilter<"GardeConfig"> | number
  }

  export type GardeUpsertWithWhereUniqueWithoutMedecinAssistantInput = {
    where: GardeWhereUniqueInput
    update: XOR<GardeUpdateWithoutMedecinAssistantInput, GardeUncheckedUpdateWithoutMedecinAssistantInput>
    create: XOR<GardeCreateWithoutMedecinAssistantInput, GardeUncheckedCreateWithoutMedecinAssistantInput>
  }

  export type GardeUpdateWithWhereUniqueWithoutMedecinAssistantInput = {
    where: GardeWhereUniqueInput
    data: XOR<GardeUpdateWithoutMedecinAssistantInput, GardeUncheckedUpdateWithoutMedecinAssistantInput>
  }

  export type GardeUpdateManyWithWhereWithoutMedecinAssistantInput = {
    where: GardeScalarWhereInput
    data: XOR<GardeUpdateManyMutationInput, GardeUncheckedUpdateManyWithoutMedecinAssistantInput>
  }

  export type GardeScalarWhereInput = {
    AND?: GardeScalarWhereInput | GardeScalarWhereInput[]
    OR?: GardeScalarWhereInput[]
    NOT?: GardeScalarWhereInput | GardeScalarWhereInput[]
    id?: IntFilter<"Garde"> | number
    date?: StringFilter<"Garde"> | string
    medecinAssistantId?: IntNullableFilter<"Garde"> | number | null
    medecinResidantId?: IntNullableFilter<"Garde"> | number | null
    mois?: StringFilter<"Garde"> | string
    annee?: IntFilter<"Garde"> | number
    createdAt?: DateTimeFilter<"Garde"> | Date | string
  }

  export type GardeUpsertWithWhereUniqueWithoutMedecinResidantInput = {
    where: GardeWhereUniqueInput
    update: XOR<GardeUpdateWithoutMedecinResidantInput, GardeUncheckedUpdateWithoutMedecinResidantInput>
    create: XOR<GardeCreateWithoutMedecinResidantInput, GardeUncheckedCreateWithoutMedecinResidantInput>
  }

  export type GardeUpdateWithWhereUniqueWithoutMedecinResidantInput = {
    where: GardeWhereUniqueInput
    data: XOR<GardeUpdateWithoutMedecinResidantInput, GardeUncheckedUpdateWithoutMedecinResidantInput>
  }

  export type GardeUpdateManyWithWhereWithoutMedecinResidantInput = {
    where: GardeScalarWhereInput
    data: XOR<GardeUpdateManyMutationInput, GardeUncheckedUpdateManyWithoutMedecinResidantInput>
  }

  export type MedecinCreateWithoutGardesAssistantInput = {
    nom: string
    prenom: string
    type: $Enums.TypeMedecin
    createdAt?: Date | string
    gardesEnTantQueAssistant?: GardeCreateNestedManyWithoutMedecinAssistantInput
    gardesEnTantQueResidant?: GardeCreateNestedManyWithoutMedecinResidantInput
  }

  export type MedecinUncheckedCreateWithoutGardesAssistantInput = {
    id?: number
    nom: string
    prenom: string
    type: $Enums.TypeMedecin
    createdAt?: Date | string
    gardesEnTantQueAssistant?: GardeUncheckedCreateNestedManyWithoutMedecinAssistantInput
    gardesEnTantQueResidant?: GardeUncheckedCreateNestedManyWithoutMedecinResidantInput
  }

  export type MedecinCreateOrConnectWithoutGardesAssistantInput = {
    where: MedecinWhereUniqueInput
    create: XOR<MedecinCreateWithoutGardesAssistantInput, MedecinUncheckedCreateWithoutGardesAssistantInput>
  }

  export type MedecinUpsertWithoutGardesAssistantInput = {
    update: XOR<MedecinUpdateWithoutGardesAssistantInput, MedecinUncheckedUpdateWithoutGardesAssistantInput>
    create: XOR<MedecinCreateWithoutGardesAssistantInput, MedecinUncheckedCreateWithoutGardesAssistantInput>
    where?: MedecinWhereInput
  }

  export type MedecinUpdateToOneWithWhereWithoutGardesAssistantInput = {
    where?: MedecinWhereInput
    data: XOR<MedecinUpdateWithoutGardesAssistantInput, MedecinUncheckedUpdateWithoutGardesAssistantInput>
  }

  export type MedecinUpdateWithoutGardesAssistantInput = {
    nom?: StringFieldUpdateOperationsInput | string
    prenom?: StringFieldUpdateOperationsInput | string
    type?: EnumTypeMedecinFieldUpdateOperationsInput | $Enums.TypeMedecin
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    gardesEnTantQueAssistant?: GardeUpdateManyWithoutMedecinAssistantNestedInput
    gardesEnTantQueResidant?: GardeUpdateManyWithoutMedecinResidantNestedInput
  }

  export type MedecinUncheckedUpdateWithoutGardesAssistantInput = {
    id?: IntFieldUpdateOperationsInput | number
    nom?: StringFieldUpdateOperationsInput | string
    prenom?: StringFieldUpdateOperationsInput | string
    type?: EnumTypeMedecinFieldUpdateOperationsInput | $Enums.TypeMedecin
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    gardesEnTantQueAssistant?: GardeUncheckedUpdateManyWithoutMedecinAssistantNestedInput
    gardesEnTantQueResidant?: GardeUncheckedUpdateManyWithoutMedecinResidantNestedInput
  }

  export type MedecinCreateWithoutGardesEnTantQueAssistantInput = {
    nom: string
    prenom: string
    type: $Enums.TypeMedecin
    createdAt?: Date | string
    gardesAssistant?: GardeConfigCreateNestedManyWithoutMedecinInput
    gardesEnTantQueResidant?: GardeCreateNestedManyWithoutMedecinResidantInput
  }

  export type MedecinUncheckedCreateWithoutGardesEnTantQueAssistantInput = {
    id?: number
    nom: string
    prenom: string
    type: $Enums.TypeMedecin
    createdAt?: Date | string
    gardesAssistant?: GardeConfigUncheckedCreateNestedManyWithoutMedecinInput
    gardesEnTantQueResidant?: GardeUncheckedCreateNestedManyWithoutMedecinResidantInput
  }

  export type MedecinCreateOrConnectWithoutGardesEnTantQueAssistantInput = {
    where: MedecinWhereUniqueInput
    create: XOR<MedecinCreateWithoutGardesEnTantQueAssistantInput, MedecinUncheckedCreateWithoutGardesEnTantQueAssistantInput>
  }

  export type MedecinCreateWithoutGardesEnTantQueResidantInput = {
    nom: string
    prenom: string
    type: $Enums.TypeMedecin
    createdAt?: Date | string
    gardesAssistant?: GardeConfigCreateNestedManyWithoutMedecinInput
    gardesEnTantQueAssistant?: GardeCreateNestedManyWithoutMedecinAssistantInput
  }

  export type MedecinUncheckedCreateWithoutGardesEnTantQueResidantInput = {
    id?: number
    nom: string
    prenom: string
    type: $Enums.TypeMedecin
    createdAt?: Date | string
    gardesAssistant?: GardeConfigUncheckedCreateNestedManyWithoutMedecinInput
    gardesEnTantQueAssistant?: GardeUncheckedCreateNestedManyWithoutMedecinAssistantInput
  }

  export type MedecinCreateOrConnectWithoutGardesEnTantQueResidantInput = {
    where: MedecinWhereUniqueInput
    create: XOR<MedecinCreateWithoutGardesEnTantQueResidantInput, MedecinUncheckedCreateWithoutGardesEnTantQueResidantInput>
  }

  export type MedecinUpsertWithoutGardesEnTantQueAssistantInput = {
    update: XOR<MedecinUpdateWithoutGardesEnTantQueAssistantInput, MedecinUncheckedUpdateWithoutGardesEnTantQueAssistantInput>
    create: XOR<MedecinCreateWithoutGardesEnTantQueAssistantInput, MedecinUncheckedCreateWithoutGardesEnTantQueAssistantInput>
    where?: MedecinWhereInput
  }

  export type MedecinUpdateToOneWithWhereWithoutGardesEnTantQueAssistantInput = {
    where?: MedecinWhereInput
    data: XOR<MedecinUpdateWithoutGardesEnTantQueAssistantInput, MedecinUncheckedUpdateWithoutGardesEnTantQueAssistantInput>
  }

  export type MedecinUpdateWithoutGardesEnTantQueAssistantInput = {
    nom?: StringFieldUpdateOperationsInput | string
    prenom?: StringFieldUpdateOperationsInput | string
    type?: EnumTypeMedecinFieldUpdateOperationsInput | $Enums.TypeMedecin
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    gardesAssistant?: GardeConfigUpdateManyWithoutMedecinNestedInput
    gardesEnTantQueResidant?: GardeUpdateManyWithoutMedecinResidantNestedInput
  }

  export type MedecinUncheckedUpdateWithoutGardesEnTantQueAssistantInput = {
    id?: IntFieldUpdateOperationsInput | number
    nom?: StringFieldUpdateOperationsInput | string
    prenom?: StringFieldUpdateOperationsInput | string
    type?: EnumTypeMedecinFieldUpdateOperationsInput | $Enums.TypeMedecin
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    gardesAssistant?: GardeConfigUncheckedUpdateManyWithoutMedecinNestedInput
    gardesEnTantQueResidant?: GardeUncheckedUpdateManyWithoutMedecinResidantNestedInput
  }

  export type MedecinUpsertWithoutGardesEnTantQueResidantInput = {
    update: XOR<MedecinUpdateWithoutGardesEnTantQueResidantInput, MedecinUncheckedUpdateWithoutGardesEnTantQueResidantInput>
    create: XOR<MedecinCreateWithoutGardesEnTantQueResidantInput, MedecinUncheckedCreateWithoutGardesEnTantQueResidantInput>
    where?: MedecinWhereInput
  }

  export type MedecinUpdateToOneWithWhereWithoutGardesEnTantQueResidantInput = {
    where?: MedecinWhereInput
    data: XOR<MedecinUpdateWithoutGardesEnTantQueResidantInput, MedecinUncheckedUpdateWithoutGardesEnTantQueResidantInput>
  }

  export type MedecinUpdateWithoutGardesEnTantQueResidantInput = {
    nom?: StringFieldUpdateOperationsInput | string
    prenom?: StringFieldUpdateOperationsInput | string
    type?: EnumTypeMedecinFieldUpdateOperationsInput | $Enums.TypeMedecin
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    gardesAssistant?: GardeConfigUpdateManyWithoutMedecinNestedInput
    gardesEnTantQueAssistant?: GardeUpdateManyWithoutMedecinAssistantNestedInput
  }

  export type MedecinUncheckedUpdateWithoutGardesEnTantQueResidantInput = {
    id?: IntFieldUpdateOperationsInput | number
    nom?: StringFieldUpdateOperationsInput | string
    prenom?: StringFieldUpdateOperationsInput | string
    type?: EnumTypeMedecinFieldUpdateOperationsInput | $Enums.TypeMedecin
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    gardesAssistant?: GardeConfigUncheckedUpdateManyWithoutMedecinNestedInput
    gardesEnTantQueAssistant?: GardeUncheckedUpdateManyWithoutMedecinAssistantNestedInput
  }

  export type GardeConfigCreateManyMedecinInput = {
    id?: number
    nombreGardes: number
    mois: string
    annee: number
  }

  export type GardeCreateManyMedecinAssistantInput = {
    id?: number
    date: string
    medecinResidantId?: number | null
    mois: string
    annee: number
    createdAt?: Date | string
  }

  export type GardeCreateManyMedecinResidantInput = {
    id?: number
    date: string
    medecinAssistantId?: number | null
    mois: string
    annee: number
    createdAt?: Date | string
  }

  export type GardeConfigUpdateWithoutMedecinInput = {
    nombreGardes?: IntFieldUpdateOperationsInput | number
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
  }

  export type GardeConfigUncheckedUpdateWithoutMedecinInput = {
    id?: IntFieldUpdateOperationsInput | number
    nombreGardes?: IntFieldUpdateOperationsInput | number
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
  }

  export type GardeConfigUncheckedUpdateManyWithoutMedecinInput = {
    id?: IntFieldUpdateOperationsInput | number
    nombreGardes?: IntFieldUpdateOperationsInput | number
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
  }

  export type GardeUpdateWithoutMedecinAssistantInput = {
    date?: StringFieldUpdateOperationsInput | string
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    medecinResidant?: MedecinUpdateOneWithoutGardesEnTantQueResidantNestedInput
  }

  export type GardeUncheckedUpdateWithoutMedecinAssistantInput = {
    id?: IntFieldUpdateOperationsInput | number
    date?: StringFieldUpdateOperationsInput | string
    medecinResidantId?: NullableIntFieldUpdateOperationsInput | number | null
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GardeUncheckedUpdateManyWithoutMedecinAssistantInput = {
    id?: IntFieldUpdateOperationsInput | number
    date?: StringFieldUpdateOperationsInput | string
    medecinResidantId?: NullableIntFieldUpdateOperationsInput | number | null
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GardeUpdateWithoutMedecinResidantInput = {
    date?: StringFieldUpdateOperationsInput | string
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    medecinAssistant?: MedecinUpdateOneWithoutGardesEnTantQueAssistantNestedInput
  }

  export type GardeUncheckedUpdateWithoutMedecinResidantInput = {
    id?: IntFieldUpdateOperationsInput | number
    date?: StringFieldUpdateOperationsInput | string
    medecinAssistantId?: NullableIntFieldUpdateOperationsInput | number | null
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type GardeUncheckedUpdateManyWithoutMedecinResidantInput = {
    id?: IntFieldUpdateOperationsInput | number
    date?: StringFieldUpdateOperationsInput | string
    medecinAssistantId?: NullableIntFieldUpdateOperationsInput | number | null
    mois?: StringFieldUpdateOperationsInput | string
    annee?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}