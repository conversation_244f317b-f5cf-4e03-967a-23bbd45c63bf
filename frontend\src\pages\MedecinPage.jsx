import { useState } from 'react';
import MedecinForm from '../components/MedecinForm';
import MedecinList from '../components/MedecinList';

const MedecinPage = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [showForm, setShowForm] = useState(false);

  const handleMedecinAdded = () => {
    setRefreshTrigger(prev => prev + 1);
    setShowForm(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            👥 Gestion des Médecins
          </h2>
          <p className="text-gray-600 mt-1">Ajoutez, modifiez et gérez les médecins de votre équipe</p>
        </div>
        <button
          onClick={() => setShowForm(!showForm)}
          className={`px-6 py-3 rounded-xl transition-all duration-300 flex items-center gap-2 font-semibold ${
            showForm
              ? 'bg-gray-500 text-white hover:bg-gray-600 hover:shadow-lg'
              : 'bg-gradient-to-r from-red-500 to-sky-500 text-white hover:from-red-600 hover:to-sky-600 hover:shadow-lg hover:-translate-y-0.5'
          }`}
        >
          {showForm ? '❌ Annuler' : '➕ Ajouter un médecin'}
        </button>
      </div>

      {/* Formulaire d'ajout */}
      {showForm && (
        <div className="bg-white rounded-2xl shadow-lg border border-red-100 p-8">
          <h3 className="text-xl font-bold bg-gradient-to-r from-red-600 to-sky-600 bg-clip-text text-transparent mb-6 flex items-center gap-2">
            ➕ Nouveau médecin
          </h3>
          <MedecinForm onMedecinAdded={handleMedecinAdded} />
        </div>
      )}

      {/* Liste des médecins */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-8 border-b border-gray-100">
          <h3 className="text-xl font-bold bg-gradient-to-r from-red-600 to-sky-600 bg-clip-text text-transparent flex items-center gap-2">
            📋 Liste des médecins
          </h3>
          <div className="flex gap-3">
            <button className="px-4 py-2 text-sm bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 transition-all duration-300 flex items-center gap-2 hover:shadow-lg">
              📊 Exporter
            </button>
            <button
              className="px-4 py-2 text-sm bg-gradient-to-r from-red-500 to-sky-500 text-white rounded-lg hover:from-red-600 hover:to-sky-600 transition-all duration-300 flex items-center gap-2 hover:shadow-lg"
              onClick={() => setRefreshTrigger(prev => prev + 1)}
            >
              🔄 Actualiser
            </button>
          </div>
        </div>
        <div className="p-8">
          <MedecinList refreshTrigger={refreshTrigger} />
        </div>
      </div>
    </div>
  );
};

export default MedecinPage;
