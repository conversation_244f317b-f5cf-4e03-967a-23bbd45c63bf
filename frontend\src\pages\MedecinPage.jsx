import { useState } from 'react';
import MedecinForm from '../components/MedecinForm';
import MedecinList from '../components/MedecinList';

const MedecinPage = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [showForm, setShowForm] = useState(false);

  const handleMedecinAdded = () => {
    setRefreshTrigger(prev => prev + 1);
    setShowForm(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            👥 Gestion des Médecins
          </h2>
          <p className="text-gray-600 mt-1">Ajoutez, modifiez et gérez les médecins de votre équipe</p>
        </div>
        <button
          onClick={() => setShowForm(!showForm)}
          className={`px-4 py-2 rounded-lg transition-colors flex items-center gap-2 ${
            showForm
              ? 'bg-gray-600 text-white hover:bg-gray-700'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {showForm ? '❌ Annuler' : '➕ Ajouter un médecin'}
        </button>
      </div>

      {/* Formulaire d'ajout */}
      {showForm && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            ➕ Nouveau médecin
          </h3>
          <MedecinForm onMedecinAdded={handleMedecinAdded} />
        </div>
      )}

      {/* Liste des médecins */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            📋 Liste des médecins
          </h3>
          <div className="flex gap-2">
            <button className="px-3 py-1.5 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-1">
              📊 Exporter
            </button>
            <button
              className="px-3 py-1.5 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-1"
              onClick={() => setRefreshTrigger(prev => prev + 1)}
            >
              🔄 Actualiser
            </button>
          </div>
        </div>
        <div className="p-6">
          <MedecinList refreshTrigger={refreshTrigger} />
        </div>
      </div>
    </div>
  );
};

export default MedecinPage;
