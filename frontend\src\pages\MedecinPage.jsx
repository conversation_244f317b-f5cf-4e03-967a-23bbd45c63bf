import { useState } from 'react';
import MedecinForm from '../components/MedecinForm';
import MedecinList from '../components/MedecinList';

const MedecinPage = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [showForm, setShowForm] = useState(false);

  const handleMedecinAdded = () => {
    setRefreshTrigger(prev => prev + 1);
    setShowForm(false);
  };

  return (
    <div className="medecin-page">
      <div className="page-header">
        <div className="header-content">
          <h2>👥 Gestion des Médecins</h2>
          <p>Ajou<PERSON>z, modifiez et gérez les médecins de votre équipe</p>
        </div>
        <div className="header-actions">
          <button
            onClick={() => setShowForm(!showForm)}
            className={`btn ${showForm ? 'btn-secondary' : 'btn-primary'}`}
          >
            {showForm ? '❌ Annuler' : '➕ Ajouter un médecin'}
          </button>
        </div>
      </div>

      <div className="page-content">
        {/* Formulaire d'ajout */}
        {showForm && (
          <div className="form-section">
            <div className="section-card">
              <h3>➕ Nouveau médecin</h3>
              <MedecinForm onMedecinAdded={handleMedecinAdded} />
            </div>
          </div>
        )}

        {/* Liste des médecins */}
        <div className="list-section">
          <div className="section-card">
            <div className="card-header">
              <h3>📋 Liste des médecins</h3>
              <div className="card-actions">
                <button className="btn btn-secondary btn-small">
                  📊 Exporter
                </button>
                <button 
                  className="btn btn-secondary btn-small"
                  onClick={() => setRefreshTrigger(prev => prev + 1)}
                >
                  🔄 Actualiser
                </button>
              </div>
            </div>
            <MedecinList refreshTrigger={refreshTrigger} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MedecinPage;
