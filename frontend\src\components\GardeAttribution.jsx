import { useState, useEffect } from 'react';
import { medecinService, gardeService } from '../services/api';

const GardeAttribution = ({ onGardesGenerated }) => {
  const [medecins, setMedecins] = useState([]);
  const [configurations, setConfigurations] = useState({});
  const [selectedMonth, setSelectedMonth] = useState('');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [typeJour, setTypeJour] = useState('SEMAINE'); // SEMAINE ou WEEKEND
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const mois = [
    'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
    'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
  ];

  const loadMedecins = async () => {
    try {
      const data = await medecinService.getAll();
      setMedecins(data);

      // Initialiser les configurations à 0 pour chaque médecin
      const initialConfigs = {};
      data.forEach(medecin => {
        initialConfigs[medecin.id] = 0;
      });
      setConfigurations(initialConfigs);
    } catch (err) {
      setError('Erreur lors du chargement des médecins');
    }
  };

  const updateGardeCount = (medecinId, delta) => {
    setConfigurations(prev => ({
      ...prev,
      [medecinId]: Math.max(0, (prev[medecinId] || 0) + delta)
    }));
  };

  const handleGenerate = async () => {
    if (!selectedMonth) {
      setError('Veuillez sélectionner un mois');
      return;
    }

    const totalGardes = Object.values(configurations).reduce((sum, count) => sum + count, 0);
    if (totalGardes === 0) {
      setError('Veuillez attribuer au moins une garde');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Préparer les configurations pour l'API
      const configArray = Object.entries(configurations)
        .filter(([_, count]) => count > 0)
        .map(([medecinId, nombreGardes]) => ({
          medecinId: parseInt(medecinId),
          nombreGardes
        }));

      // Sauvegarder la configuration
      await gardeService.saveConfiguration(configArray, selectedMonth, selectedYear);

      // Ouvrir la page de planning drag & drop
      if (onGardesGenerated) {
        onGardesGenerated(null, selectedMonth, selectedYear, typeJour, true);
      }

    } catch (err) {
      setError(err.response?.data?.error || 'Erreur lors de la génération');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMedecins();
  }, []);

  if (medecins.length === 0) {
    return (
      <div className="garde-attribution">
        <div className="empty-state">
          <p>Aucun médecin disponible</p>
          <p>Veuillez d'abord ajouter des médecins avant d'attribuer des gardes.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="garde-attribution">

      {error && (
        <div className="error-message">
          ❌ {error}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div>
          <label htmlFor="month" className="block text-sm font-medium text-gray-700 mb-1">
            Mois :
          </label>
          <select
            id="month"
            value={selectedMonth}
            onChange={(e) => setSelectedMonth(e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Sélectionner un mois</option>
            {mois.map((mois, index) => (
              <option key={index} value={mois}>
                {mois.charAt(0).toUpperCase() + mois.slice(1)}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-1">
            Année :
          </label>
          <input
            type="number"
            id="year"
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
            min="2024"
            max="2030"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label htmlFor="typeJour" className="block text-sm font-medium text-gray-700 mb-1">
            Type de planning :
          </label>
          <select
            id="typeJour"
            value={typeJour}
            onChange={(e) => setTypeJour(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="SEMAINE">Semaine (Dim-Jeu) - 16h00-8h00</option>
            <option value="WEEKEND">Weekend (Ven-Sam) - 2 gardes/jour</option>
          </select>
        </div>
      </div>

      <div className="mb-6">
        {typeJour === 'SEMAINE' ? (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-lg font-semibold text-blue-800 mb-2 flex items-center gap-2">
              📅 Planning Semaine
            </h4>
            <p className="text-blue-700 mb-1">
              <strong>Jours :</strong> Dimanche, Lundi, Mardi, Mercredi, Jeudi
            </p>
            <p className="text-blue-700">
              <strong>Horaires :</strong> 16h00 - 8h00 (garde de nuit)
            </p>
          </div>
        ) : (
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <h4 className="text-lg font-semibold text-purple-800 mb-2 flex items-center gap-2">
              🌙 Planning Weekend
            </h4>
            <p className="text-purple-700 mb-1">
              <strong>Jours :</strong> Vendredi, Samedi
            </p>
            <p className="text-purple-700 mb-1">
              <strong>Garde 1 :</strong> 8h00 - 20h00 (jour)
            </p>
            <p className="text-purple-700">
              <strong>Garde 2 :</strong> 20h00 - 8h00 (nuit)
            </p>
          </div>
        )}
      </div>

      <div className="medecin-attribution">
        <h3>Nombre de gardes par médecin :</h3>
        <div className="medecin-grid">
          {medecins.map((medecin) => (
            <div key={medecin.id} className="medecin-card">
              <div className="medecin-info">
                <h4>{medecin.prenom} {medecin.nom}</h4>
                <span className={`type-badge ${medecin.type.toLowerCase()}`}>
                  {medecin.type === 'Residant' ? '🎓 Résidant' : '👨‍⚕️ Assistant'}
                </span>
              </div>

              <div className="garde-counter">
                <button
                  onClick={() => updateGardeCount(medecin.id, -1)}
                  className="btn btn-counter"
                  disabled={configurations[medecin.id] === 0}
                >
                  ➖
                </button>

                <span className="garde-count">
                  {configurations[medecin.id] || 0} garde{(configurations[medecin.id] || 0) > 1 ? 's' : ''}
                </span>

                <button
                  onClick={() => updateGardeCount(medecin.id, 1)}
                  className="btn btn-counter"
                >
                  ➕
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="border-t border-gray-200 pt-6 mt-6">
        <div className="text-center mb-4">
          <span className="text-lg font-semibold text-gray-700">
            Total des gardes : {Object.values(configurations).reduce((sum, count) => sum + count, 0)}
          </span>
        </div>

        <div className="flex justify-center">
          <button
            onClick={handleGenerate}
            disabled={loading || !selectedMonth}
            className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-lg font-semibold flex items-center gap-2"
          >
            {loading ? '⏳ Génération...' : '🎯 Générer la liste des gardes'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default GardeAttribution;
