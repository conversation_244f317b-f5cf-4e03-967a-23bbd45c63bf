import { useState, useEffect } from 'react';
import { medecinService, gardeService } from '../services/api';

const GardeAttribution = ({ onGardesGenerated }) => {
  const [medecins, setMedecins] = useState([]);
  const [configurations, setConfigurations] = useState({});
  const [selectedMonth, setSelectedMonth] = useState('');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [typeJour, setTypeJour] = useState('SEMAINE'); // SEMAINE ou WEEKEND
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const mois = [
    'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
    'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
  ];

  const loadMedecins = async () => {
    try {
      const data = await medecinService.getAll();
      setMedecins(data);

      // Initialiser les configurations à 0 pour chaque médecin
      const initialConfigs = {};
      data.forEach(medecin => {
        initialConfigs[medecin.id] = 0;
      });
      setConfigurations(initialConfigs);
    } catch (err) {
      setError('Erreur lors du chargement des médecins');
    }
  };

  const updateGardeCount = (medecinId, delta) => {
    setConfigurations(prev => ({
      ...prev,
      [medecinId]: Math.max(0, (prev[medecinId] || 0) + delta)
    }));
  };

  const handleGenerate = async () => {
    if (!selectedMonth) {
      setError('Veuillez sélectionner un mois');
      return;
    }

    const totalGardes = Object.values(configurations).reduce((sum, count) => sum + count, 0);
    if (totalGardes === 0) {
      setError('Veuillez attribuer au moins une garde');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Préparer les configurations pour l'API
      const configArray = Object.entries(configurations)
        .filter(([_, count]) => count > 0)
        .map(([medecinId, nombreGardes]) => ({
          medecinId: parseInt(medecinId),
          nombreGardes
        }));

      // Sauvegarder la configuration
      await gardeService.saveConfiguration(configArray, selectedMonth, selectedYear);

      // Ouvrir la page de planning drag & drop
      if (onGardesGenerated) {
        onGardesGenerated(null, selectedMonth, selectedYear, typeJour, true);
      }

    } catch (err) {
      setError(err.response?.data?.error || 'Erreur lors de la génération');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMedecins();
  }, []);

  if (medecins.length === 0) {
    return (
      <div className="garde-attribution">
        <div className="empty-state">
          <p>Aucun médecin disponible</p>
          <p>Veuillez d'abord ajouter des médecins avant d'attribuer des gardes.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="garde-attribution">

      {error && (
        <div className="error-message">
          ❌ {error}
        </div>
      )}

      <div className="config-grid">
        <div className="form-group">
          <label htmlFor="month">Mois :</label>
          <select
            id="month"
            value={selectedMonth}
            onChange={(e) => setSelectedMonth(e.target.value)}
            required
          >
            <option value="">Sélectionner un mois</option>
            {mois.map((mois, index) => (
              <option key={index} value={mois}>
                {mois.charAt(0).toUpperCase() + mois.slice(1)}
              </option>
            ))}
          </select>
        </div>

        <div className="form-group">
          <label htmlFor="year">Année :</label>
          <input
            type="number"
            id="year"
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
            min="2024"
            max="2030"
          />
        </div>

        <div className="form-group">
          <label htmlFor="typeJour">Type de planning :</label>
          <select
            id="typeJour"
            value={typeJour}
            onChange={(e) => setTypeJour(e.target.value)}
          >
            <option value="SEMAINE">Semaine (Dim-Jeu) - 16h00-8h00</option>
            <option value="WEEKEND">Weekend (Ven-Sam) - 2 gardes/jour</option>
          </select>
        </div>
      </div>

      <div className="horaire-info">
        {typeJour === 'SEMAINE' ? (
          <div className="info-card">
            <h4>📅 Planning Semaine</h4>
            <p><strong>Jours :</strong> Dimanche, Lundi, Mardi, Mercredi, Jeudi</p>
            <p><strong>Horaires :</strong> 16h00 - 8h00 (garde de nuit)</p>
          </div>
        ) : (
          <div className="info-card">
            <h4>🌙 Planning Weekend</h4>
            <p><strong>Jours :</strong> Vendredi, Samedi</p>
            <p><strong>Garde 1 :</strong> 8h00 - 20h00 (jour)</p>
            <p><strong>Garde 2 :</strong> 20h00 - 8h00 (nuit)</p>
          </div>
        )}
      </div>

      <div className="medecin-attribution">
        <h3>Nombre de gardes par médecin :</h3>
        <div className="medecin-grid">
          {medecins.map((medecin) => (
            <div key={medecin.id} className="medecin-card">
              <div className="medecin-info">
                <h4>{medecin.prenom} {medecin.nom}</h4>
                <span className={`type-badge ${medecin.type.toLowerCase()}`}>
                  {medecin.type === 'Residant' ? '🎓 Résidant' : '👨‍⚕️ Assistant'}
                </span>
              </div>

              <div className="garde-counter">
                <button
                  onClick={() => updateGardeCount(medecin.id, -1)}
                  className="btn btn-counter"
                  disabled={configurations[medecin.id] === 0}
                >
                  ➖
                </button>

                <span className="garde-count">
                  {configurations[medecin.id] || 0} garde{(configurations[medecin.id] || 0) > 1 ? 's' : ''}
                </span>

                <button
                  onClick={() => updateGardeCount(medecin.id, 1)}
                  className="btn btn-counter"
                >
                  ➕
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="generate-section">
        <div className="total-gardes">
          Total des gardes : {Object.values(configurations).reduce((sum, count) => sum + count, 0)}
        </div>

        <button
          onClick={handleGenerate}
          disabled={loading || !selectedMonth}
          className="btn btn-primary btn-large"
        >
          {loading ? '⏳ Génération...' : '🎯 Générer la liste des gardes'}
        </button>
      </div>
    </div>
  );
};

export default GardeAttribution;
