import { useState } from 'react';
import { medecinService } from '../services/api';

const MedecinForm = ({ onMedecinAdded }) => {
  const [formData, setFormData] = useState({
    nom: '',
    prenom: '',
    type: 'Residant'
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Validation
      if (!formData.nom.trim() || !formData.prenom.trim()) {
        throw new Error('Le nom et le prénom sont requis');
      }

      const medecin = await medecinService.create({
        nom: formData.nom.trim(),
        prenom: formData.prenom.trim(),
        type: formData.type
      });

      // Réinitialiser le formulaire
      setFormData({
        nom: '',
        prenom: '',
        type: 'Residant'
      });

      // Notifier le parent
      if (onMedecinAdded) {
        onMedecinAdded(medecin);
      }

    } catch (err) {
      setError(err.response?.data?.error || err.message || 'Erreur lors de l\'ajout');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="medecin-form">
      {error && (
        <div className="error-message">
          ❌ {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="nom">Nom :</label>
          <input
            type="text"
            id="nom"
            name="nom"
            value={formData.nom}
            onChange={handleChange}
            placeholder="Nom du médecin"
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="prenom">Prénom :</label>
          <input
            type="text"
            id="prenom"
            name="prenom"
            value={formData.prenom}
            onChange={handleChange}
            placeholder="Prénom du médecin"
            required
          />
        </div>

        <div className="form-group">
          <label htmlFor="type">Type :</label>
          <select
            id="type"
            name="type"
            value={formData.type}
            onChange={handleChange}
            required
          >
            <option value="Residant">Résidant</option>
            <option value="Assistant">Assistant</option>
          </select>
        </div>

        <button
          type="submit"
          disabled={loading}
          className="btn btn-primary"
        >
          {loading ? '⏳ Ajout...' : '➕ Ajouter le médecin'}
        </button>
      </form>
    </div>
  );
};

export default MedecinForm;
