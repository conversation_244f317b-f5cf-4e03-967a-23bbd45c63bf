# 🏥 Gestion des Gardes Médicales

Application web moderne et intuitive pour la gestion et la planification des gardes médicales, développée avec React et Node.js. Interface drag & drop pour une attribution facile des gardes.

## 🚀 Fonctionnalités

### 🧱 1. Gestion des médecins
- **Frontend (React)** :
  - Formulaire d'ajout avec nom, prénom et type (Résidant/Assistant)
  - Affichage des médecins dans un tableau
  - Suppression des médecins avec confirmation

- **Backend (Node.js + Express)** :
  - `POST /api/medecins` - Ajouter un médecin
  - `GET /api/medecins` - Récupérer tous les médecins
  - `DELETE /api/medecins/:id` - Supprimer un médecin

### 📆 2. Attribution des gardes
- **Frontend** :
  - Interface pour attribuer le nombre de gardes par médecin
  - Sélection du mois et de l'année
  - Compteurs avec boutons ➕ ➖ pour chaque médecin
  - Bouton "Générer la liste"

- **Backend** :
  - `POST /api/gardes/configuration` - Sauvegarder la configuration des gardes

### 🗓️ 3. Interface de planning drag & drop
- **Frontend** :
  - **Page de planning interactive** qui s'ouvre après la configuration
  - **Interface drag & drop** pour attribuer facilement les médecins aux dates
  - **Sidebar avec médecins** organisés par type (Assistant/Résidant)
  - **Calendrier visuel** avec slots pour chaque type de médecin
  - **Suppression facile** des attributions avec bouton ✕
  - **Design responsive** adapté mobile et desktop

- **Backend** :
  - `POST /api/gardes/save` - Sauvegarder un planning complet
  - `GET /api/gardes/:mois/:annee` - Récupérer les gardes
  - `DELETE /api/gardes/:id` - Supprimer une garde

### 📝 4. Exportation Word (.docx)
- **Frontend** :
  - Bouton "Exporter en Word" après génération
  - Téléchargement automatique du fichier

- **Backend** :
  - Génération de documents Word formatés avec la librairie `docx`
  - `GET /api/gardes/export/:mois/:annee` - Exporter en Word

## 🛠️ Technologies utilisées

### Backend
- **Node.js** avec Express
- **Prisma** ORM pour la gestion de base de données
- **PostgreSQL** (configurable pour SQLite)
- **docx** pour l'exportation Word
- **CORS** pour les requêtes cross-origin

### Frontend
- **React** avec Vite
- **Axios** pour les appels API
- **CSS moderne** avec variables CSS et design responsive

### Base de données
- **PostgreSQL** (par défaut)
- **Prisma** pour les migrations et la gestion du schéma
- Tables : `medecins`, `garde_config`, `gardes`

## 📦 Installation et démarrage

### Prérequis
- Node.js (v16 ou plus récent)
- PostgreSQL (ou SQLite pour le développement)
- npm ou yarn

### 1. Cloner le projet
```bash
git clone <url-du-repo>
cd garde
```

### 2. Configuration de la base de données

#### Option A : PostgreSQL (recommandé)
1. Créer une base de données PostgreSQL nommée `garde_service`
2. Configurer le fichier `backend/.env` :
```env
DATABASE_URL="postgresql://username:password@localhost:5432/garde_service?schema=public"
PORT=3001
```

#### Option B : SQLite (développement)
Modifier `backend/prisma/schema.prisma` :
```prisma
datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}
```

### 3. Installation des dépendances

#### Backend
```bash
cd backend
npm install
npx prisma generate
npx prisma db push
```

#### Frontend
```bash
cd frontend
npm install
```

### 4. Démarrage de l'application

#### Backend (Terminal 1)
```bash
cd backend
npm run dev
```
Le serveur démarre sur http://localhost:3001

#### Frontend (Terminal 2)
```bash
cd frontend
npm run dev
```
L'application démarre sur http://localhost:5173

## 🎯 Utilisation

1. **Ajouter des médecins** : Utilisez le formulaire pour ajouter des résidants et assistants
2. **Configurer les gardes** : Sélectionnez un mois et attribuez le nombre de gardes par médecin
3. **Générer le planning** : Cliquez sur "Générer la liste" pour créer automatiquement le planning
4. **Exporter** : Téléchargez le planning au format Word (.docx)

## 🔧 Configuration

### Variables d'environnement (backend/.env)
```env
DATABASE_URL="postgresql://username:password@localhost:5432/garde_service?schema=public"
PORT=3001
```

### Algorithme de répartition
L'algorithme respecte les contraintes suivantes :
- Maximum 3 gardes par semaine par médecin
- Répartition équitable selon les quotas définis
- Pas de doublons le même jour pour un médecin
- Mélange aléatoire pour éviter les patterns

## 📊 Structure de la base de données

```sql
-- Table des médecins
medecins (
  id SERIAL PRIMARY KEY,
  nom VARCHAR NOT NULL,
  prenom VARCHAR NOT NULL,
  type ENUM('Residant', 'Assistant') NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
)

-- Configuration des gardes par médecin/mois
garde_config (
  id SERIAL PRIMARY KEY,
  medecin_id INTEGER REFERENCES medecins(id),
  nombre_gardes INTEGER NOT NULL,
  mois VARCHAR NOT NULL,
  annee INTEGER NOT NULL
)

-- Planning des gardes générées
gardes (
  id SERIAL PRIMARY KEY,
  date VARCHAR NOT NULL,
  medecin_assistant_id INTEGER REFERENCES medecins(id),
  medecin_residant_id INTEGER REFERENCES medecins(id),
  mois VARCHAR NOT NULL,
  annee INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
)
```

## 🚀 Déploiement

### Backend
1. Configurer les variables d'environnement de production
2. Exécuter `npm run build` si nécessaire
3. Déployer sur votre plateforme (Heroku, Railway, etc.)

### Frontend
1. Modifier l'URL de l'API dans `src/services/api.js`
2. Exécuter `npm run build`
3. Déployer les fichiers statiques (Netlify, Vercel, etc.)

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Commit les changements (`git commit -am 'Ajout nouvelle fonctionnalité'`)
4. Push vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Créer une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 👥 Auteurs

- Développé avec ❤️ pour la gestion des gardes médicales
- Utilise Prisma pour une gestion optimale de la base de données
