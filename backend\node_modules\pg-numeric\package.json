{"name": "pg-numeric", "version": "1.0.2", "description": "reads PostgreSQL binary format for numeric values into a string", "bugs": "https://github.com/charmander/pg-numeric/issues", "license": "ISC", "files": ["index.js"], "repository": {"type": "git", "url": "https://github.com/charmander/pg-numeric"}, "scripts": {"test": "node test"}, "devDependencies": {"@charmander/eslint-config-base": "1.2.0"}, "engines": {"node": ">=4"}}