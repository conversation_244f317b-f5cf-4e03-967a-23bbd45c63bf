import { useState, useEffect } from 'react';
import { medecinService, dateExclusionService } from '../services/api';

const Dashboard = ({ onNavigate }) => {
  const [stats, setStats] = useState({
    totalMedecins: 0,
    assistants: 0,
    residants: 0,
    exclusions: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Charger les statistiques des médecins
      const medecins = await medecinService.getAll();

      // Charger les exclusions
      const exclusions = await dateExclusionService.getAll();

      setStats({
        totalMedecins: medecins.length,
        assistants: medecins.filter(m => m.type === 'Assistant').length,
        residants: medecins.filter(m => m.type === 'Residant').length,
        exclusions: exclusions.length
      });

    } catch (error) {
      console.error('Erreur lors du chargement du dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: 'Ajouter un médecin',
      description: 'Ajouter un nouveau médecin au système',
      icon: '👨‍⚕️',
      action: () => onNavigate('medecins'),
      color: 'primary'
    },
    {
      title: 'Configurer les gardes',
      description: 'Créer un nouveau planning de gardes',
      icon: '⚙️',
      action: () => onNavigate('garde-config'),
      color: 'success'
    },
    {
      title: 'Gérer les exclusions',
      description: 'Définir les jours fériés et exclusions',
      icon: '📅',
      action: () => onNavigate('date-exclusion'),
      color: 'warning'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement du dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">📊 Dashboard</h2>
        <p className="text-gray-600">Vue d'ensemble de votre système de gestion des gardes</p>
      </div>

      {/* Cartes de statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center">
            <div className="text-3xl mr-4">👥</div>
            <div>
              <h3 className="text-2xl font-bold text-blue-600">{stats.totalMedecins}</h3>
              <p className="text-gray-600 text-sm">Médecins total</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center">
            <div className="text-3xl mr-4">👨‍⚕️</div>
            <div>
              <h3 className="text-2xl font-bold text-green-600">{stats.assistants}</h3>
              <p className="text-gray-600 text-sm">Assistants</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center">
            <div className="text-3xl mr-4">🎓</div>
            <div>
              <h3 className="text-2xl font-bold text-yellow-600">{stats.residants}</h3>
              <p className="text-gray-600 text-sm">Résidants</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center">
            <div className="text-3xl mr-4">📅</div>
            <div>
              <h3 className="text-2xl font-bold text-purple-600">{stats.exclusions}</h3>
              <p className="text-gray-600 text-sm">Dates d'exclusion</p>
            </div>
          </div>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center gap-2">
          🚀 Actions rapides
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className="p-4 rounded-lg border-2 border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 text-left group"
            >
              <div className="text-2xl mb-2">{action.icon}</div>
              <div>
                <h4 className="font-semibold text-gray-900 group-hover:text-blue-700 mb-1">
                  {action.title}
                </h4>
                <p className="text-sm text-gray-600 group-hover:text-blue-600">
                  {action.description}
                </p>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
