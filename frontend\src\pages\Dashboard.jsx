import { useState, useEffect } from 'react';
import { medecinService, dateExclusionService } from '../services/api';

const Dashboard = ({ onNavigate }) => {
  const [stats, setStats] = useState({
    totalMedecins: 0,
    assistants: 0,
    residants: 0,
    exclusions: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Charger les statistiques des médecins
      const medecins = await medecinService.getAll();

      // Charger les exclusions
      const exclusions = await dateExclusionService.getAll();

      setStats({
        totalMedecins: medecins.length,
        assistants: medecins.filter(m => m.type === 'Assistant').length,
        residants: medecins.filter(m => m.type === 'Residant').length,
        exclusions: exclusions.length
      });

    } catch (error) {
      console.error('Erreur lors du chargement du dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: 'Ajouter un médecin',
      description: 'Ajouter un nouveau médecin au système',
      icon: '👨‍⚕️',
      action: () => onNavigate('medecins'),
      color: 'primary'
    },
    {
      title: 'Configurer les gardes',
      description: 'Créer un nouveau planning de gardes',
      icon: '⚙️',
      action: () => onNavigate('garde-config'),
      color: 'success'
    },
    {
      title: 'Gérer les exclusions',
      description: 'Définir les jours fériés et exclusions',
      icon: '📅',
      action: () => onNavigate('date-exclusion'),
      color: 'warning'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement du dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h2 className="text-4xl font-bold bg-gradient-to-r from-red-600 to-sky-600 bg-clip-text text-transparent mb-3">
          📊 Dashboard
        </h2>
        <p className="text-gray-600 text-lg">Vue d'ensemble de votre système de gestion des gardes</p>
      </div>

      {/* Cartes de statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl shadow-lg border border-red-100 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-3xl font-bold text-red-600">{stats.totalMedecins}</h3>
              <p className="text-gray-600 text-sm font-medium">Médecins total</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center">
              <span className="text-white text-2xl">👥</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg border border-sky-100 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-3xl font-bold text-sky-600">{stats.assistants}</h3>
              <p className="text-gray-600 text-sm font-medium">Assistants</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-sky-500 to-sky-600 rounded-xl flex items-center justify-center">
              <span className="text-white text-2xl">👨‍⚕️</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg border border-purple-100 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-3xl font-bold text-purple-600">{stats.residants}</h3>
              <p className="text-gray-600 text-sm font-medium">Résidants</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
              <span className="text-white text-2xl">🎓</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg border border-green-100 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-3xl font-bold text-green-600">{stats.exclusions}</h3>
              <p className="text-gray-600 text-sm font-medium">Dates d'exclusion</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
              <span className="text-white text-2xl">📅</span>
            </div>
          </div>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8">
        <h3 className="text-2xl font-bold bg-gradient-to-r from-red-600 to-sky-600 bg-clip-text text-transparent mb-8 flex items-center gap-3">
          🚀 Actions rapides
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className="p-6 rounded-xl border-2 border-gray-100 hover:border-red-200 hover:bg-gradient-to-br hover:from-red-50 hover:to-sky-50 transition-all duration-300 text-left group hover:shadow-lg hover:-translate-y-1"
            >
              <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-sky-500 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                <span className="text-white text-xl">{action.icon}</span>
              </div>
              <div>
                <h4 className="font-bold text-gray-900 group-hover:text-red-700 mb-2 text-lg">
                  {action.title}
                </h4>
                <p className="text-sm text-gray-600 group-hover:text-gray-700">
                  {action.description}
                </p>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
