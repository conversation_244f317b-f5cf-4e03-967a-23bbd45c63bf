import { useState, useEffect } from 'react';
import { medecinService, gardeService } from '../services/api';

const Dashboard = ({ onNavigate }) => {
  const [stats, setStats] = useState({
    totalMedecins: 0,
    assistants: 0,
    residants: 0,
    planningsActifs: 0
  });
  const [recentActivity, setRecentActivity] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Charger les statistiques des médecins
      const medecins = await medecinService.getAll();
      
      setStats({
        totalMedecins: medecins.length,
        assistants: medecins.filter(m => m.type === 'Assistant').length,
        residants: medecins.filter(m => m.type === 'Residant').length,
        planningsActifs: 0 // À implémenter selon vos besoins
      });

      // Activité récente simulée
      setRecentActivity([
        { id: 1, action: 'Médecin ajouté', details: 'Dr. <PERSON> (Résidant)', time: '2 heures' },
        { id: 2, action: 'Planning généré', details: 'Janvier 2024', time: '1 jour' },
        { id: 3, action: 'Export Word', details: 'Planning Décembre 2023', time: '2 jours' }
      ]);

    } catch (error) {
      console.error('Erreur lors du chargement du dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: 'Ajouter un médecin',
      description: 'Ajouter un nouveau médecin au système',
      icon: '👨‍⚕️',
      action: () => onNavigate('medecins'),
      color: 'primary'
    },
    {
      title: 'Configurer les gardes',
      description: 'Créer un nouveau planning de gardes',
      icon: '⚙️',
      action: () => onNavigate('garde-config'),
      color: 'success'
    },
    {
      title: 'Gérer les exclusions',
      description: 'Définir les jours fériés et exclusions',
      icon: '📅',
      action: () => onNavigate('date-exclusion'),
      color: 'warning'
    }
  ];

  if (loading) {
    return (
      <div className="dashboard loading">
        <div className="spinner"></div>
        <p>Chargement du dashboard...</p>
      </div>
    );
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h2>📊 Dashboard</h2>
        <p>Vue d'ensemble de votre système de gestion des gardes</p>
      </div>

      {/* Cartes de statistiques */}
      <div className="stats-grid">
        <div className="stat-card primary">
          <div className="stat-icon">👥</div>
          <div className="stat-content">
            <h3>{stats.totalMedecins}</h3>
            <p>Médecins total</p>
          </div>
        </div>

        <div className="stat-card success">
          <div className="stat-icon">👨‍⚕️</div>
          <div className="stat-content">
            <h3>{stats.assistants}</h3>
            <p>Assistants</p>
          </div>
        </div>

        <div className="stat-card warning">
          <div className="stat-icon">🎓</div>
          <div className="stat-content">
            <h3>{stats.residants}</h3>
            <p>Résidants</p>
          </div>
        </div>

        <div className="stat-card info">
          <div className="stat-icon">📋</div>
          <div className="stat-content">
            <h3>{stats.planningsActifs}</h3>
            <p>Plannings actifs</p>
          </div>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="quick-actions">
        <h3>🚀 Actions rapides</h3>
        <div className="actions-grid">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className={`action-card ${action.color}`}
            >
              <div className="action-icon">{action.icon}</div>
              <div className="action-content">
                <h4>{action.title}</h4>
                <p>{action.description}</p>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Activité récente */}
      <div className="recent-activity">
        <h3>📈 Activité récente</h3>
        <div className="activity-list">
          {recentActivity.map(activity => (
            <div key={activity.id} className="activity-item">
              <div className="activity-content">
                <h4>{activity.action}</h4>
                <p>{activity.details}</p>
              </div>
              <div className="activity-time">
                Il y a {activity.time}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
