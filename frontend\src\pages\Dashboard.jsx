import { useState, useEffect } from 'react';
import { medecinService, dateExclusionService } from '../services/api';

const Dashboard = ({ onNavigate }) => {
  const [stats, setStats] = useState({
    totalMedecins: 0,
    assistants: 0,
    residants: 0,
    exclusions: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Charger les statistiques des médecins
      const medecins = await medecinService.getAll();

      // Charger les exclusions
      const exclusions = await dateExclusionService.getAll();

      setStats({
        totalMedecins: medecins.length,
        assistants: medecins.filter(m => m.type === 'Assistant').length,
        residants: medecins.filter(m => m.type === 'Residant').length,
        exclusions: exclusions.length
      });

    } catch (error) {
      console.error('Erreur lors du chargement du dashboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const quickActions = [
    {
      title: 'Ajouter un médecin',
      description: 'Ajouter un nouveau médecin au système',
      icon: '👨‍⚕️',
      action: () => onNavigate('medecins'),
      color: 'primary'
    },
    {
      title: 'Configurer les gardes',
      description: 'Créer un nouveau planning de gardes',
      icon: '⚙️',
      action: () => onNavigate('garde-config'),
      color: 'success'
    },
    {
      title: 'Gérer les exclusions',
      description: 'Définir les jours fériés et exclusions',
      icon: '📅',
      action: () => onNavigate('date-exclusion'),
      color: 'warning'
    }
  ];

  if (loading) {
    return (
      <div className="dashboard loading">
        <div className="spinner"></div>
        <p>Chargement du dashboard...</p>
      </div>
    );
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h2>📊 Dashboard</h2>
        <p>Vue d'ensemble de votre système de gestion des gardes</p>
      </div>

      {/* Cartes de statistiques */}
      <div className="stats-grid">
        <div className="stat-card primary">
          <div className="stat-icon">👥</div>
          <div className="stat-content">
            <h3>{stats.totalMedecins}</h3>
            <p>Médecins total</p>
          </div>
        </div>

        <div className="stat-card success">
          <div className="stat-icon">👨‍⚕️</div>
          <div className="stat-content">
            <h3>{stats.assistants}</h3>
            <p>Assistants</p>
          </div>
        </div>

        <div className="stat-card warning">
          <div className="stat-icon">🎓</div>
          <div className="stat-content">
            <h3>{stats.residants}</h3>
            <p>Résidants</p>
          </div>
        </div>

        <div className="stat-card info">
          <div className="stat-icon">📅</div>
          <div className="stat-content">
            <h3>{stats.exclusions}</h3>
            <p>Dates d'exclusion</p>
          </div>
        </div>
      </div>

      {/* Actions rapides */}
      <div className="quick-actions">
        <h3>🚀 Actions rapides</h3>
        <div className="actions-grid">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className={`action-card ${action.color}`}
            >
              <div className="action-icon">{action.icon}</div>
              <div className="action-content">
                <h4>{action.title}</h4>
                <p>{action.description}</p>
              </div>
            </button>
          ))}
        </div>
      </div>


    </div>
  );
};

export default Dashboard;
