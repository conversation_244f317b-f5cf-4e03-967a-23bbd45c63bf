import { useState } from 'react';
import MedecinForm from './components/MedecinForm';
import MedecinList from './components/MedecinList';
import GardeAttribution from './components/GardeAttribution';
import GardeTable from './components/GardeTable';
import GardePlanningPage from './components/GardePlanningPage';
import './App.css';

function App() {
  const [refreshMedecins, setRefreshMedecins] = useState(0);
  const [gardes, setGardes] = useState([]);
  const [currentMonth, setCurrentMonth] = useState('');
  const [currentYear, setCurrentYear] = useState(null);
  const [showPlanningPage, setShowPlanningPage] = useState(false);

  const handleMedecinAdded = () => {
    // Déclencher un refresh de la liste des médecins
    setRefreshMedecins(prev => prev + 1);
  };

  const handleGardesGenerated = (newGardes, mois, annee, openPlanningPage = false) => {
    if (openPlanningPage) {
      setCurrentMonth(mois);
      setCurrentYear(annee);
      setShowPlanningPage(true);
    } else {
      setGardes(newGardes || []);
      setCurrentMonth(mois);
      setCurrentYear(annee);
    }
  };

  const handleGardeDeleted = (gardeId) => {
    setGardes(prev => prev.filter(garde => garde.id !== gardeId));
  };

  const handleClosePlanningPage = () => {
    setShowPlanningPage(false);
    // Optionnel : recharger les gardes sauvegardées
    // loadGardes(currentMonth, currentYear);
  };

  return (
    <div className="app">
      {/* Page de planning drag & drop */}
      {showPlanningPage && (
        <GardePlanningPage
          mois={currentMonth}
          annee={currentYear}
          onClose={handleClosePlanningPage}
        />
      )}

      {/* Interface principale */}
      {!showPlanningPage && (
        <>
          <header className="app-header">
            <div className="header-content">
              <div>
                <h1>🏥 Gestion des Gardes Médicales</h1>
                <p>Application professionnelle de planification des gardes</p>
              </div>
              <nav className="header-nav">
                <span className="nav-item active">Dashboard</span>
                <span className="nav-item">Plannings</span>
                <span className="nav-item">Rapports</span>
              </nav>
            </div>
          </header>

          <main className="app-main">
            {/* Colonne gauche */}
            <div className="left-column">
              {/* Section 1: Gestion des médecins */}
              <section className="section">
                <div className="section-content">
                  <h2 className="section-title">👥 Gestion des Médecins</h2>
                  <MedecinForm onMedecinAdded={handleMedecinAdded} />
                </div>
              </section>

              <section className="section">
                <div className="section-content">
                  <h2 className="section-title">📋 Liste des Médecins</h2>
                  <MedecinList refreshTrigger={refreshMedecins} />
                </div>
              </section>
            </div>

            {/* Colonne droite */}
            <div className="right-column">
              {/* Section 2: Attribution des gardes */}
              <section className="section">
                <div className="section-content">
                  <h2 className="section-title">📅 Configuration des Gardes</h2>
                  <GardeAttribution onGardesGenerated={handleGardesGenerated} />
                </div>
              </section>

              {/* Section 3: Tableau des gardes générées */}
              {gardes.length > 0 && (
                <section className="section">
                  <div className="section-content">
                    <h2 className="section-title">🗓️ Planning Généré</h2>
                    <GardeTable
                      gardes={gardes}
                      mois={currentMonth}
                      annee={currentYear}
                      onGardeDeleted={handleGardeDeleted}
                    />
                  </div>
                </section>
              )}
            </div>
          </main>

          <footer className="app-footer">
            <p>© 2024 - Système de gestion des gardes médicales - Version 1.0</p>
          </footer>
        </>
      )}
    </div>
  );
}

export default App;
