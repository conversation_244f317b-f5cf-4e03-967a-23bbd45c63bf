import { useState } from 'react';
import Dashboard from './pages/Dashboard';
import MedecinPage from './pages/MedecinPage';
import GardeConfigPage from './pages/GardeConfigPage';
import DateExclusionPage from './pages/DateExclusionPage';
import GardePlanningPage from './components/GardePlanningPage';
import Sidebar from './components/Sidebar';
import './App.css';

function App() {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [currentMonth, setCurrentMonth] = useState('');
  const [currentYear, setCurrentYear] = useState(null);
  const [showPlanningPage, setShowPlanningPage] = useState(false);

  const handleNavigate = (page) => {
    setCurrentPage(page);
  };

  const handleGardesGenerated = (newGardes, mois, annee, openPlanningPage = false) => {
    if (openPlanningPage) {
      setCurrentMonth(mois);
      setCurrentYear(annee);
      setShowPlanningPage(true);
    }
  };

  const handleClosePlanningPage = () => {
    setShowPlanningPage(false);
    setCurrentPage('dashboard');
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'medecins':
        return <MedecinPage />;
      case 'garde-config':
        return <GardeConfigPage onGardesGenerated={handleGardesGenerated} />;
      case 'date-exclusion':
        return <DateExclusionPage />;
      default:
        return <Dashboard onNavigate={handleNavigate} />;
    }
  };

  return (
    <div className="app">
      {/* Page de planning drag & drop */}
      {showPlanningPage && (
        <GardePlanningPage
          mois={currentMonth}
          annee={currentYear}
          onClose={handleClosePlanningPage}
        />
      )}

      {/* Interface principale avec sidebar */}
      {!showPlanningPage && (
        <div className="app-layout">
          <Sidebar currentPage={currentPage} onNavigate={handleNavigate} />

          <div className="main-content">
            <header className="content-header">
              <h1>🏥 Gestion des Gardes Médicales</h1>
            </header>

            <main className="content-main">
              {renderCurrentPage()}
            </main>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;
