import { useState } from 'react';
import MedecinForm from './components/MedecinForm';
import MedecinList from './components/MedecinList';
import GardeAttribution from './components/GardeAttribution';
import GardeTable from './components/GardeTable';
import GardePlanningPage from './components/GardePlanningPage';
import './App.css';

function App() {
  const [refreshMedecins, setRefreshMedecins] = useState(0);
  const [gardes, setGardes] = useState([]);
  const [currentMonth, setCurrentMonth] = useState('');
  const [currentYear, setCurrentYear] = useState(null);
  const [showPlanningPage, setShowPlanningPage] = useState(false);

  const handleMedecinAdded = () => {
    // Déclencher un refresh de la liste des médecins
    setRefreshMedecins(prev => prev + 1);
  };

  const handleGardesGenerated = (newGardes, mois, annee, openPlanningPage = false) => {
    if (openPlanningPage) {
      setCurrentMonth(mois);
      setCurrentYear(annee);
      setShowPlanningPage(true);
    } else {
      setGardes(newGardes || []);
      setCurrentMonth(mois);
      setCurrentYear(annee);
    }
  };

  const handleGardeDeleted = (gardeId) => {
    setGardes(prev => prev.filter(garde => garde.id !== gardeId));
  };

  const handleClosePlanningPage = () => {
    setShowPlanningPage(false);
    // Optionnel : recharger les gardes sauvegardées
    // loadGardes(currentMonth, currentYear);
  };

  return (
    <div className="app">
      {/* Page de planning drag & drop */}
      {showPlanningPage && (
        <GardePlanningPage
          mois={currentMonth}
          annee={currentYear}
          onClose={handleClosePlanningPage}
        />
      )}

      {/* Interface principale */}
      {!showPlanningPage && (
        <>
          <header className="app-header">
            <h1>🏥 Gestion des Gardes Médicales</h1>
            <p>Application moderne de planification des gardes pour les médecins</p>
          </header>

          <main className="app-main">
            {/* Section 1: Gestion des médecins */}
            <section className="section">
              <div className="section-content">
                <MedecinForm onMedecinAdded={handleMedecinAdded} />
                <MedecinList refreshTrigger={refreshMedecins} />
              </div>
            </section>

            {/* Section 2: Attribution des gardes */}
            <section className="section">
              <div className="section-content">
                <GardeAttribution onGardesGenerated={handleGardesGenerated} />
              </div>
            </section>

            {/* Section 3: Tableau des gardes générées */}
            {gardes.length > 0 && (
              <section className="section">
                <div className="section-content">
                  <GardeTable
                    gardes={gardes}
                    mois={currentMonth}
                    annee={currentYear}
                    onGardeDeleted={handleGardeDeleted}
                  />
                </div>
              </section>
            )}
          </main>

          <footer className="app-footer">
            <p>© 2024 - Système de gestion des gardes médicales</p>
          </footer>
        </>
      )}
    </div>
  );
}

export default App;
