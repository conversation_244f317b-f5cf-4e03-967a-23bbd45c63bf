import { useState } from 'react';
import Dashboard from './pages/Dashboard';
import MedecinPage from './pages/MedecinPage';
import GardeConfigPage from './pages/GardeConfigPage';
import DateExclusionPage from './pages/DateExclusionPage';
import GardePlanningPage from './components/GardePlanningPage';
import Sidebar from './components/Sidebar';


function App() {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [currentMonth, setCurrentMonth] = useState('');
  const [currentYear, setCurrentYear] = useState(null);
  const [currentTypeJour, setCurrentTypeJour] = useState('SEMAINE');
  const [showPlanningPage, setShowPlanningPage] = useState(false);

  const handleNavigate = (page) => {
    setCurrentPage(page);
  };

  const handleGardesGenerated = (newGardes, mois, annee, typeJour, openPlanningPage = false) => {
    if (openPlanningPage) {
      setCurrentMonth(mois);
      setCurrentYear(annee);
      setCurrentTypeJour(typeJour);
      setShowPlanningPage(true);
    }
  };

  const handleClosePlanningPage = () => {
    setShowPlanningPage(false);
    setCurrentPage('dashboard');
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'medecins':
        return <MedecinPage />;
      case 'garde-config':
        return <GardeConfigPage onGardesGenerated={handleGardesGenerated} />;
      case 'date-exclusion':
        return <DateExclusionPage />;
      default:
        return <Dashboard onNavigate={handleNavigate} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page de planning drag & drop */}
      {showPlanningPage && (
        <GardePlanningPage
          mois={currentMonth}
          annee={currentYear}
          typeJour={currentTypeJour}
          onClose={handleClosePlanningPage}
        />
      )}

      {/* Interface principale avec sidebar */}
      {!showPlanningPage && (
        <div className="flex min-h-screen bg-gradient-to-br from-gray-50 to-red-50/30">
          <Sidebar currentPage={currentPage} onNavigate={handleNavigate} />

          <div className="flex-1 ml-72">
            <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 px-8 py-6 shadow-sm">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-red-600 to-sky-600 bg-clip-text text-transparent flex items-center gap-3">
                    🏥 Gestion des Gardes Médicales
                  </h1>
                  <p className="text-gray-600 mt-1">Système de planification avancé</p>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-sky-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">👨‍⚕️</span>
                  </div>
                </div>
              </div>
            </header>

            <main className="p-8 max-w-7xl mx-auto">
              {renderCurrentPage()}
            </main>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;
