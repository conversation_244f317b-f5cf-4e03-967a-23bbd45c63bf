import { useState } from 'react';
import Dashboard from './pages/Dashboard';
import MedecinPage from './pages/MedecinPage';
import GardeConfigPage from './pages/GardeConfigPage';
import DateExclusionPage from './pages/DateExclusionPage';
import GardePlanningPage from './components/GardePlanningPage';
import Sidebar from './components/Sidebar';
import './App.css';

function App() {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [currentMonth, setCurrentMonth] = useState('');
  const [currentYear, setCurrentYear] = useState(null);
  const [currentTypeJour, setCurrentTypeJour] = useState('SEMAINE');
  const [showPlanningPage, setShowPlanningPage] = useState(false);

  const handleNavigate = (page) => {
    setCurrentPage(page);
  };

  const handleGardesGenerated = (newGardes, mois, annee, typeJour, openPlanningPage = false) => {
    if (openPlanningPage) {
      setCurrentMonth(mois);
      setCurrentYear(annee);
      setCurrentTypeJour(typeJour);
      setShowPlanningPage(true);
    }
  };

  const handleClosePlanningPage = () => {
    setShowPlanningPage(false);
    setCurrentPage('dashboard');
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'medecins':
        return <MedecinPage />;
      case 'garde-config':
        return <GardeConfigPage onGardesGenerated={handleGardesGenerated} />;
      case 'date-exclusion':
        return <DateExclusionPage />;
      default:
        return <Dashboard onNavigate={handleNavigate} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Page de planning drag & drop */}
      {showPlanningPage && (
        <GardePlanningPage
          mois={currentMonth}
          annee={currentYear}
          typeJour={currentTypeJour}
          onClose={handleClosePlanningPage}
        />
      )}

      {/* Interface principale avec sidebar */}
      {!showPlanningPage && (
        <div className="flex min-h-screen bg-gray-50">
          <Sidebar currentPage={currentPage} onNavigate={handleNavigate} />

          <div className="flex-1 ml-64">
            <header className="bg-white border-b border-gray-200 px-6 py-4 shadow-sm">
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                🏥 Gestion des Gardes Médicales
              </h1>
            </header>

            <main className="p-6 max-w-7xl mx-auto">
              {renderCurrentPage()}
            </main>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;
