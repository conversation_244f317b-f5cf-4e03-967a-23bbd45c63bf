const express = require('express');
const { PrismaClient } = require('@prisma/client');

const router = express.Router();
const prisma = new PrismaClient();

// GET /api/date-exclusions - R<PERSON>cupérer toutes les dates d'exclusion
router.get('/', async (req, res) => {
  try {
    const exclusions = await prisma.dateExclusion.findMany({
      orderBy: { date: 'asc' }
    });
    res.json(exclusions);
  } catch (error) {
    console.error('Erreur lors de la récupération des exclusions:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// POST /api/date-exclusions - Ajouter une date d'exclusion
router.post('/', async (req, res) => {
  try {
    const { date, raison, type } = req.body;

    // Validation
    if (!date || !raison || !type) {
      return res.status(400).json({ error: 'Date, raison et type sont requis' });
    }

    const validTypes = ['FERIE', 'CONGE', 'FORMATION', 'AUTRE'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({ error: 'Type invalide' });
    }

    const exclusion = await prisma.dateExclusion.create({
      data: {
        date: date,
        raison: raison.trim(),
        type: type
      }
    });

    res.status(201).json(exclusion);
  } catch (error) {
    if (error.code === 'P2002') {
      return res.status(400).json({ error: 'Cette date est déjà exclue' });
    }
    console.error('Erreur lors de l\'ajout de l\'exclusion:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// DELETE /api/date-exclusions/:id - Supprimer une date d'exclusion
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const exclusionId = parseInt(id);

    if (isNaN(exclusionId)) {
      return res.status(400).json({ error: 'ID invalide' });
    }

    await prisma.dateExclusion.delete({
      where: { id: exclusionId }
    });

    res.json({ message: 'Exclusion supprimée avec succès' });
  } catch (error) {
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Exclusion non trouvée' });
    }
    console.error('Erreur lors de la suppression de l\'exclusion:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// POST /api/date-exclusions/reset - Réinitialiser aux jours fériés par défaut
router.post('/reset', async (req, res) => {
  try {
    // Supprimer toutes les exclusions existantes
    await prisma.dateExclusion.deleteMany();

    // Jours fériés français par défaut pour 2024
    const defaultHolidays = [
      { date: '2024-01-01', raison: 'Jour de l\'An', type: 'FERIE' },
      { date: '2024-05-01', raison: 'Fête du Travail', type: 'FERIE' },
      { date: '2024-05-08', raison: 'Fête de la Victoire', type: 'FERIE' },
      { date: '2024-07-14', raison: 'Fête Nationale', type: 'FERIE' },
      { date: '2024-08-15', raison: 'Assomption', type: 'FERIE' },
      { date: '2024-11-01', raison: 'Toussaint', type: 'FERIE' },
      { date: '2024-11-11', raison: 'Armistice', type: 'FERIE' },
      { date: '2024-12-25', raison: 'Noël', type: 'FERIE' }
    ];

    // Ajouter les jours fériés par défaut
    await prisma.dateExclusion.createMany({
      data: defaultHolidays
    });

    const exclusions = await prisma.dateExclusion.findMany({
      orderBy: { date: 'asc' }
    });

    res.json({ 
      message: 'Exclusions réinitialisées aux jours fériés par défaut',
      exclusions 
    });
  } catch (error) {
    console.error('Erreur lors de la réinitialisation:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

module.exports = router;
