# `@prisma/engines`

⚠️ **Warning**: This package is intended for Prisma's internal use.
Its release cycle does not follow SemVer, which means we might release breaking changes (change APIs, remove functionality) without any prior warning.

The postinstall hook of this package downloads all Prisma engines available for the current platform, namely the Query Engine and the Schema Engine from the Prisma CDN.

The engines version to be downloaded is directly determined by the version of its `@prisma/engines-version` dependency.

You should probably not use this package directly, but instead use one of these:

- [`prisma` CLI](https://www.npmjs.com/package/prisma)
- [`@prisma/client`](https://www.npmjs.com/package/@prisma/client)
