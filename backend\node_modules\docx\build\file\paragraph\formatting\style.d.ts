import { XmlComponent } from '../../xml-components';
export declare const HeadingLevel: {
    readonly HEADING_1: "Heading1";
    readonly HEADING_2: "Heading2";
    readonly HEADING_3: "Heading3";
    readonly HEADING_4: "Heading4";
    readonly HEADING_5: "Heading5";
    readonly HEADING_6: "Heading6";
    readonly TITLE: "Title";
};
export declare class Style extends XmlComponent {
    constructor(styleId: string);
}
