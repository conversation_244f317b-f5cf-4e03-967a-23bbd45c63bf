const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class Database {
  constructor() {
    this.db = new sqlite3.Database(path.join(__dirname, '../garde.db'));
    this.init();
  }

  init() {
    // Table des médecins
    this.db.run(`
      CREATE TABLE IF NOT EXISTS medecins (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        prenom TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('Résidant', 'Assistant')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Table des gardes
    this.db.run(`
      CREATE TABLE IF NOT EXISTS gardes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT NOT NULL,
        medecin_assistant_id INTEGER,
        medecin_residant_id INTEGER,
        mois TEXT NOT NULL,
        annee INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        <PERSON>OR<PERSON><PERSON><PERSON> KEY (medecin_assistant_id) REFERENCES medecins (id),
        FOREIGN KEY (medecin_residant_id) REFERENCES medecins (id)
      )
    `);

    // Table de configuration des gardes par médecin
    this.db.run(`
      CREATE TABLE IF NOT EXISTS garde_config (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        medecin_id INTEGER NOT NULL,
        nombre_gardes INTEGER NOT NULL,
        mois TEXT NOT NULL,
        annee INTEGER NOT NULL,
        FOREIGN KEY (medecin_id) REFERENCES medecins (id)
      )
    `);
  }

  // Méthodes pour les médecins
  getAllMedecins() {
    return new Promise((resolve, reject) => {
      this.db.all('SELECT * FROM medecins ORDER BY nom, prenom', (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  addMedecin(nom, prenom, type) {
    return new Promise((resolve, reject) => {
      this.db.run(
        'INSERT INTO medecins (nom, prenom, type) VALUES (?, ?, ?)',
        [nom, prenom, type],
        function(err) {
          if (err) reject(err);
          else resolve({ id: this.lastID, nom, prenom, type });
        }
      );
    });
  }

  deleteMedecin(id) {
    return new Promise((resolve, reject) => {
      this.db.run('DELETE FROM medecins WHERE id = ?', [id], function(err) {
        if (err) reject(err);
        else resolve({ changes: this.changes });
      });
    });
  }

  // Méthodes pour les gardes
  saveGardeConfig(configs) {
    return new Promise((resolve, reject) => {
      const { mois, annee } = configs[0];
      
      // Supprimer l'ancienne configuration pour ce mois
      this.db.run('DELETE FROM garde_config WHERE mois = ? AND annee = ?', [mois, annee], (err) => {
        if (err) {
          reject(err);
          return;
        }

        // Insérer la nouvelle configuration
        const stmt = this.db.prepare('INSERT INTO garde_config (medecin_id, nombre_gardes, mois, annee) VALUES (?, ?, ?, ?)');
        
        configs.forEach(config => {
          stmt.run([config.medecin_id, config.nombre_gardes, config.mois, config.annee]);
        });
        
        stmt.finalize((err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    });
  }

  saveGardes(gardes) {
    return new Promise((resolve, reject) => {
      const { mois, annee } = gardes[0];
      
      // Supprimer les anciennes gardes pour ce mois
      this.db.run('DELETE FROM gardes WHERE mois = ? AND annee = ?', [mois, annee], (err) => {
        if (err) {
          reject(err);
          return;
        }

        // Insérer les nouvelles gardes
        const stmt = this.db.prepare('INSERT INTO gardes (date, medecin_assistant_id, medecin_residant_id, mois, annee) VALUES (?, ?, ?, ?, ?)');
        
        gardes.forEach(garde => {
          stmt.run([garde.date, garde.medecin_assistant_id, garde.medecin_residant_id, garde.mois, garde.annee]);
        });
        
        stmt.finalize((err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    });
  }

  getGardes(mois, annee) {
    return new Promise((resolve, reject) => {
      const query = `
        SELECT g.*, 
               ma.nom as assistant_nom, ma.prenom as assistant_prenom,
               mr.nom as residant_nom, mr.prenom as residant_prenom
        FROM gardes g
        LEFT JOIN medecins ma ON g.medecin_assistant_id = ma.id
        LEFT JOIN medecins mr ON g.medecin_residant_id = mr.id
        WHERE g.mois = ? AND g.annee = ?
        ORDER BY g.date
      `;
      
      this.db.all(query, [mois, annee], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  close() {
    this.db.close();
  }
}

module.exports = Database;
