/* Variables CSS */
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --success-color: #16a34a;
  --success-hover: #15803d;
  --danger-color: #dc2626;
  --danger-hover: #b91c1c;
  --warning-color: #d97706;
  --secondary-color: #6b7280;
  --background-color: #f8fafc;
  --card-background: #ffffff;
  --border-color: #e2e8f0;
  --text-color: #1f2937;
  --text-muted: #6b7280;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Reset et base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
}

/* Layout principal */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  padding: 2rem 1rem;
  text-align: center;
  box-shadow: var(--shadow-lg);
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.app-header p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.app-main {
  flex: 1;
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.app-footer {
  background-color: var(--card-background);
  border-top: 1px solid var(--border-color);
  padding: 1rem;
  text-align: center;
  color: var(--text-muted);
}

/* Sections */
.section {
  margin-bottom: 3rem;
}

.section-content {
  background-color: var(--card-background);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

/* Formulaires */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-color);
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  gap: 0.5rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: var(--success-hover);
  transform: translateY(-1px);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: var(--danger-hover);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #4b5563;
  transform: translateY(-1px);
}

.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.btn-counter {
  background-color: var(--background-color);
  color: var(--text-color);
  border: 2px solid var(--border-color);
  padding: 0.5rem;
  min-width: 2.5rem;
  height: 2.5rem;
}

.btn-counter:hover:not(:disabled) {
  background-color: var(--border-color);
}

/* Messages d'erreur et de statut */
.error-message {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: var(--text-muted);
  font-size: 1.1rem;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-muted);
}

.empty-state p {
  margin-bottom: 0.5rem;
}

/* Badges de type */
.type-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  gap: 0.25rem;
}

.type-badge.residant {
  background-color: #dbeafe;
  color: #1e40af;
}

.type-badge.assistant {
  background-color: #dcfce7;
  color: #166534;
}

/* Tableaux */
.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.medecin-table,
.garde-planning-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--card-background);
}

.medecin-table th,
.medecin-table td,
.garde-planning-table th,
.garde-planning-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.medecin-table th,
.garde-planning-table th {
  background-color: var(--background-color);
  font-weight: 600;
  color: var(--text-color);
}

.medecin-table tr:hover,
.garde-planning-table tr:hover {
  background-color: #f8fafc;
}

/* Composant MedecinForm */
.medecin-form h2 {
  margin-bottom: 1.5rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Composant MedecinList */
.medecin-list h2 {
  margin-bottom: 1.5rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-container {
  text-align: center;
  padding: 2rem;
}

/* Composant GardeAttribution */
.garde-attribution h2 {
  margin-bottom: 1.5rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.month-selection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.medecin-attribution h3 {
  margin-bottom: 1rem;
  color: var(--text-color);
}

.medecin-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.medecin-card {
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  background-color: var(--card-background);
}

.medecin-info h4 {
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.garde-counter {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 1rem;
}

.garde-count {
  font-weight: 600;
  color: var(--text-color);
  min-width: 100px;
  text-align: center;
}

.generate-section {
  text-align: center;
  padding: 2rem;
  border-top: 1px solid var(--border-color);
}

.total-gardes {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-color);
}

/* Composant GardeTable */
.garde-table h2 {
  margin-bottom: 1.5rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.date-cell {
  font-weight: 600;
  color: var(--primary-color);
}

.jour-cell {
  color: var(--text-muted);
  font-style: italic;
}

.medecin-cell {
  min-width: 200px;
}

.medecin-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.no-medecin {
  color: var(--text-muted);
  font-style: italic;
}

.actions-cell {
  text-align: center;
  width: 100px;
}

.table-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.stats {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 1rem;
  color: var(--text-muted);
  font-size: 0.875rem;
}

/* Responsive */
@media (max-width: 768px) {
  .app-header h1 {
    font-size: 2rem;
  }

  .app-main {
    padding: 1rem 0.5rem;
  }

  .section-content {
    padding: 1rem;
  }

  .month-selection {
    grid-template-columns: 1fr;
  }

  .medecin-grid {
    grid-template-columns: 1fr;
  }

  .table-header {
    flex-direction: column;
    align-items: stretch;
  }

  .garde-counter {
    flex-direction: column;
    gap: 0.5rem;
  }

  .stats {
    flex-direction: column;
    text-align: center;
  }
}
