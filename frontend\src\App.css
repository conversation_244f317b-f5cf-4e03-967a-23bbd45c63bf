/* Variables CSS - Design Desktop Professionnel */
:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-light: #dbeafe;
  --primary-dark: #1e40af;
  --success-color: #059669;
  --success-hover: #047857;
  --success-light: #d1fae5;
  --danger-color: #dc2626;
  --danger-hover: #b91c1c;
  --danger-light: #fee2e2;
  --warning-color: #d97706;
  --warning-hover: #b45309;
  --warning-light: #fef3c7;
  --secondary-color: #6b7280;
  --secondary-hover: #4b5563;
  --background-color: #f9fafb;
  --background-secondary: #f3f4f6;
  --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --sidebar-bg: #1f2937;
  --sidebar-hover: #374151;
  --card-background: #ffffff;
  --card-hover: #f8fafc;
  --border-color: #e5e7eb;
  --border-hover: #d1d5db;
  --text-color: #111827;
  --text-muted: #6b7280;
  --text-light: #9ca3af;
  --text-white: #ffffff;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --radius-sm: 4px;
  --radius: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset et base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Layout Desktop Professionnel */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--background-color);
}

.app-header {
  background: var(--card-background);
  border-bottom: 1px solid var(--border-color);
  padding: 1.5rem 2rem;
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.app-header h1 {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.app-header p {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}

.header-nav {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-item {
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  color: var(--text-muted);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
  font-size: 0.875rem;
}

.nav-item:hover {
  background: var(--background-secondary);
  color: var(--text-color);
}

.nav-item.active {
  background: var(--primary-light);
  color: var(--primary-color);
}

.app-main {
  flex: 1;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  padding: 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

.app-footer {
  background: var(--card-background);
  border-top: 1px solid var(--border-color);
  padding: 1rem 2rem;
  text-align: center;
  color: var(--text-muted);
  font-size: 0.875rem;
}

/* Sections Desktop */
.section {
  height: fit-content;
}

.section-content {
  background: var(--card-background);
  border-radius: var(--radius-md);
  padding: 2rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  height: fit-content;
}

.section-content:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-hover);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

/* Layout en colonnes pour desktop */
.desktop-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: start;
}

.left-column {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.right-column {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Formulaires */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-color);
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Boutons modernes */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.875rem 1.75rem;
  border: none;
  border-radius: var(--radius);
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em;
  box-shadow: var(--shadow-sm);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn:disabled::before {
  display: none;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(99, 102, 241, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color), var(--success-hover));
  color: white;
  box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.3);
}

.btn-success:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(16, 185, 129, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color), var(--danger-hover));
  color: white;
  box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.3);
}

.btn-danger:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(239, 68, 68, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-hover));
  color: white;
  box-shadow: 0 4px 14px 0 rgba(100, 116, 139, 0.3);
}

.btn-secondary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(100, 116, 139, 0.4);
}

.btn-small {
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
}

.btn-large {
  padding: 1.125rem 2.25rem;
  font-size: 1.125rem;
  border-radius: var(--radius-lg);
}

.btn-counter {
  background: var(--card-background);
  color: var(--text-color);
  border: 2px solid var(--border-color);
  padding: 0.625rem;
  min-width: 2.75rem;
  height: 2.75rem;
  border-radius: var(--radius-sm);
  font-weight: 700;
  font-size: 1.1rem;
}

.btn-counter:hover:not(:disabled) {
  background: var(--primary-light);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: scale(1.05);
}

/* Messages d'erreur et de statut */
.error-message {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: var(--text-muted);
  font-size: 1.1rem;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-muted);
}

.empty-state p {
  margin-bottom: 0.5rem;
}

/* Badges de type */
.type-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  gap: 0.25rem;
}

.type-badge.residant {
  background-color: #dbeafe;
  color: #1e40af;
}

.type-badge.assistant {
  background-color: #dcfce7;
  color: #166534;
}

/* Tableaux */
.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.medecin-table,
.garde-planning-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--card-background);
}

.medecin-table th,
.medecin-table td,
.garde-planning-table th,
.garde-planning-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.medecin-table th,
.garde-planning-table th {
  background-color: var(--background-color);
  font-weight: 600;
  color: var(--text-color);
}

.medecin-table tr:hover,
.garde-planning-table tr:hover {
  background-color: #f8fafc;
}

/* Composant MedecinList */
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.count-badge {
  background: var(--primary-light);
  color: var(--primary-color);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-xl);
  font-size: 0.875rem;
  font-weight: 600;
}

.medecin-table-container {
  max-height: 400px;
  overflow-y: auto;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.medecin-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--card-background);
}

.medecin-table th {
  background: var(--background-secondary);
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.875rem;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.medecin-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
}

.medecin-table tr:hover {
  background: var(--card-hover);
}

.medecin-table tr:last-child td {
  border-bottom: none;
}

.error-container {
  text-align: center;
  padding: 2rem;
}

/* Composant GardeAttribution */
.garde-attribution h2 {
  margin-bottom: 1.5rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.month-selection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.medecin-attribution h3 {
  margin-bottom: 1rem;
  color: var(--text-color);
}

.medecin-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.medecin-card {
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: 1rem;
  background: var(--card-background);
  transition: var(--transition);
}

.medecin-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.medecin-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.medecin-info h4 {
  margin: 0;
  color: var(--text-color);
  font-size: 0.875rem;
  font-weight: 600;
}

.garde-counter {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.garde-count {
  font-weight: 600;
  color: var(--text-color);
  min-width: 80px;
  text-align: center;
  font-size: 0.875rem;
}

.generate-section {
  text-align: center;
  padding: 2rem;
  border-top: 1px solid var(--border-color);
}

.total-gardes {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-color);
}

/* Composant GardeTable */
.garde-table h2 {
  margin-bottom: 1.5rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.date-cell {
  font-weight: 600;
  color: var(--primary-color);
}

.jour-cell {
  color: var(--text-muted);
  font-style: italic;
}

.medecin-cell {
  min-width: 200px;
}

.medecin-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.no-medecin {
  color: var(--text-muted);
  font-style: italic;
}

.actions-cell {
  text-align: center;
  width: 100px;
}

.table-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.stats {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 1rem;
  color: var(--text-muted);
  font-size: 0.875rem;
}

/* Page de planning drag & drop */
.planning-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--background-color);
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.planning-header {
  background: var(--background-gradient);
  color: white;
  padding: 1.5rem 2rem;
  box-shadow: var(--shadow-lg);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-content h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.planning-container {
  flex: 1;
  display: flex;
  overflow: hidden;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.medecins-sidebar {
  width: 300px;
  background: var(--card-background);
  border-right: 1px solid var(--border-color);
  padding: 1.5rem;
  overflow-y: auto;
  box-shadow: var(--shadow);
}

.medecins-sidebar h3 {
  margin-bottom: 1.5rem;
  color: var(--text-color);
  font-size: 1.25rem;
  font-weight: 600;
}

.medecin-category {
  margin-bottom: 2rem;
}

.medecin-category h4 {
  margin-bottom: 1rem;
  color: var(--text-muted);
  font-size: 1rem;
  font-weight: 600;
}

.medecin-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.medecin-card {
  background: var(--card-background);
  border: 2px solid var(--border-color);
  border-radius: var(--radius);
  padding: 1rem;
  cursor: grab;
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.medecin-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.medecin-card:active {
  cursor: grabbing;
  transform: scale(0.98);
}

.medecin-card.draggable {
  user-select: none;
}

.medecin-name {
  font-weight: 600;
  color: var(--text-color);
}

.medecin-type {
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-weight: 500;
  width: fit-content;
}

.medecin-type.assistant {
  background: var(--success-light);
  color: var(--success-color);
}

.medecin-type.residant {
  background: var(--primary-light);
  color: var(--primary-color);
}

.calendar-container {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  background: var(--background-color);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.date-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.date-card:hover {
  box-shadow: var(--shadow);
  transform: translateY(-1px);
}

.date-header {
  background: var(--background-gradient);
  color: white;
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.date-number {
  font-size: 1.25rem;
  font-weight: 700;
}

.date-day {
  font-size: 0.875rem;
  opacity: 0.9;
  text-transform: capitalize;
}

.garde-slots {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.garde-slot {
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-sm);
  padding: 0.75rem;
  min-height: 60px;
  transition: var(--transition);
  position: relative;
}

.garde-slot:hover {
  border-color: var(--border-hover);
  background: var(--card-hover);
}

.garde-slot.assistant-slot {
  border-color: var(--success-color);
  background: var(--success-light);
}

.garde-slot.residant-slot {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.slot-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-muted);
  margin-bottom: 0.5rem;
}

.empty-slot {
  color: var(--text-light);
  font-style: italic;
  font-size: 0.875rem;
  text-align: center;
  padding: 0.5rem;
}

.assigned-medecin {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--card-background);
  border-radius: var(--radius-sm);
  padding: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.remove-btn {
  background: var(--danger-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
  transition: var(--transition);
}

.remove-btn:hover {
  background: var(--danger-hover);
  transform: scale(1.1);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .app-main {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .left-column, .right-column {
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .header-nav {
    justify-content: center;
  }

  .app-header h1 {
    font-size: 1.5rem;
  }

  .app-main {
    padding: 1rem;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .section-content {
    padding: 1.5rem;
  }

  .month-selection {
    grid-template-columns: 1fr;
  }

  .medecin-grid {
    max-height: 200px;
  }

  .medecin-table-container {
    max-height: 300px;
  }

  .garde-counter {
    gap: 0.5rem;
  }

  .btn-counter {
    min-width: 2.5rem;
    height: 2.5rem;
  }

  /* Planning page responsive */
  .planning-container {
    flex-direction: column;
  }

  .medecins-sidebar {
    width: 100%;
    max-height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .calendar-grid {
    grid-template-columns: 1fr;
  }

  .date-card {
    margin-bottom: 1rem;
  }
}
