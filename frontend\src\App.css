@tailwind base;
@tailwind components;
@tailwind utilities;

.type-badge.residant {
  background-color: #dbeafe;
  color: #1e40af;
}

.type-badge.assistant {
  background-color: #dcfce7;
  color: #166534;
}

/* Tableaux */
.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.medecin-table,
.garde-planning-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--card-background);
}

.medecin-table th,
.medecin-table td,
.garde-planning-table th,
.garde-planning-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.medecin-table th,
.garde-planning-table th {
  background-color: var(--background-color);
  font-weight: 600;
  color: var(--text-color);
}

.medecin-table tr:hover,
.garde-planning-table tr:hover {
  background-color: #f8fafc;
}

/* Composant MedecinList */
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.count-badge {
  background: var(--primary-light);
  color: var(--primary-color);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-xl);
  font-size: 0.875rem;
  font-weight: 600;
}

.medecin-table-container {
  max-height: 400px;
  overflow-y: auto;
  border-radius: var(--radius);
  border: 1px solid var(--border-color);
}

.medecin-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--card-background);
}

.medecin-table th {
  background: var(--background-secondary);
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.875rem;
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.medecin-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
}

.medecin-table tr:hover {
  background: var(--card-hover);
}

.medecin-table tr:last-child td {
  border-bottom: none;
}

.error-container {
  text-align: center;
  padding: 2rem;
}

/* Composant GardeAttribution */
.garde-attribution h2 {
  margin-bottom: 1.5rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.horaire-info {
  margin-bottom: 2rem;
}

.info-card {
  background: var(--primary-light);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius);
  padding: 1rem;
}

.info-card h4 {
  margin: 0 0 0.75rem 0;
  color: var(--primary-color);
  font-size: 1rem;
  font-weight: 600;
}

.info-card p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: var(--text-color);
}

.medecin-attribution h3 {
  margin-bottom: 1rem;
  color: var(--text-color);
}

.medecin-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.medecin-card {
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: 1rem;
  background: var(--card-background);
  transition: var(--transition);
}

.medecin-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.medecin-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.medecin-info h4 {
  margin: 0;
  color: var(--text-color);
  font-size: 0.875rem;
  font-weight: 600;
}

.garde-counter {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.garde-count {
  font-weight: 600;
  color: var(--text-color);
  min-width: 80px;
  text-align: center;
  font-size: 0.875rem;
}

.generate-section {
  text-align: center;
  padding: 2rem;
  border-top: 1px solid var(--border-color);
}

.total-gardes {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-color);
}

/* Composant GardeTable */
.garde-table h2 {
  margin-bottom: 1.5rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.date-cell {
  font-weight: 600;
  color: var(--primary-color);
}

.jour-cell {
  color: var(--text-muted);
  font-style: italic;
}

.medecin-cell {
  min-width: 200px;
}

.medecin-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.no-medecin {
  color: var(--text-muted);
  font-style: italic;
}

.actions-cell {
  text-align: center;
  width: 100px;
}

.table-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.stats {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 1rem;
  color: var(--text-muted);
  font-size: 0.875rem;
}

/* Page de planning drag & drop */
.planning-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--background-color);
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.planning-header {
  background: var(--background-gradient);
  color: white;
  padding: 1.5rem 2rem;
  box-shadow: var(--shadow-lg);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.header-content h1 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.planning-container {
  flex: 1;
  display: flex;
  overflow: hidden;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.medecins-sidebar {
  width: 300px;
  background: var(--card-background);
  border-right: 1px solid var(--border-color);
  padding: 1.5rem;
  overflow-y: auto;
  box-shadow: var(--shadow);
}

.medecins-sidebar h3 {
  margin-bottom: 1.5rem;
  color: var(--text-color);
  font-size: 1.25rem;
  font-weight: 600;
}

.medecin-category {
  margin-bottom: 2rem;
}

.medecin-category h4 {
  margin-bottom: 1rem;
  color: var(--text-muted);
  font-size: 1rem;
  font-weight: 600;
}

.medecin-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.medecin-card {
  background: var(--card-background);
  border: 2px solid var(--border-color);
  border-radius: var(--radius);
  padding: 1rem;
  cursor: grab;
  transition: var(--transition);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.medecin-card:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.medecin-card:active {
  cursor: grabbing;
  transform: scale(0.98);
}

.medecin-card.draggable {
  user-select: none;
}

.medecin-name {
  font-weight: 600;
  color: var(--text-color);
}

.medecin-type {
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-weight: 500;
  width: fit-content;
}

.medecin-type.assistant {
  background: var(--success-light);
  color: var(--success-color);
}

.medecin-type.residant {
  background: var(--primary-light);
  color: var(--primary-color);
}

.calendar-container {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  background: var(--background-color);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.date-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.date-card:hover {
  box-shadow: var(--shadow);
  transform: translateY(-1px);
}

.date-header {
  background: var(--background-gradient);
  color: white;
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.date-number {
  font-size: 1.25rem;
  font-weight: 700;
}

.date-day {
  font-size: 0.875rem;
  opacity: 0.9;
  text-transform: capitalize;
}

.garde-slots {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.garde-slot {
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-sm);
  padding: 0.75rem;
  min-height: 60px;
  transition: var(--transition);
  position: relative;
}

.garde-slot:hover {
  border-color: var(--border-hover);
  background: var(--card-hover);
}

.garde-slot.assistant-slot {
  border-color: var(--success-color);
  background: var(--success-light);
}

.garde-slot.residant-slot {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.slot-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-muted);
  margin-bottom: 0.5rem;
}

.empty-slot {
  color: var(--text-light);
  font-style: italic;
  font-size: 0.875rem;
  text-align: center;
  padding: 0.5rem;
}

.assigned-medecin {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--card-background);
  border-radius: var(--radius-sm);
  padding: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.remove-btn {
  background: var(--danger-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.75rem;
  transition: var(--transition);
}

.remove-btn:hover {
  background: var(--danger-hover);
  transform: scale(1.1);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 1rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Styles des Pages */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.header-content h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.header-content p {
  color: var(--text-muted);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.page-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
}

.section-card.info {
  background: var(--primary-light);
  border-color: var(--primary-color);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.card-actions {
  display: flex;
  gap: 0.5rem;
}

/* Dashboard */
.dashboard {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.dashboard-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0 0 0.5rem 0;
}

.dashboard-header p {
  color: var(--text-muted);
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-card.primary { border-left: 4px solid var(--primary-color); }
.stat-card.success { border-left: 4px solid var(--success-color); }
.stat-card.warning { border-left: 4px solid var(--warning-color); }
.stat-card.info { border-left: 4px solid var(--secondary-color); }

.stat-icon {
  font-size: 2rem;
}

.stat-content h3 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

.stat-content p {
  color: var(--text-muted);
  margin: 0;
  font-size: 0.875rem;
}

.quick-actions h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.action-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: var(--transition);
  text-align: left;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-card.primary:hover { border-color: var(--primary-color); }
.action-card.success:hover { border-color: var(--success-color); }
.action-card.warning:hover { border-color: var(--warning-color); }

.action-icon {
  font-size: 2rem;
}

.action-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.25rem 0;
}

.action-content p {
  color: var(--text-muted);
  margin: 0;
  font-size: 0.875rem;
}

.recent-activity h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-content h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.25rem 0;
}

.activity-content p {
  color: var(--text-muted);
  margin: 0;
  font-size: 0.75rem;
}

.activity-time {
  color: var(--text-light);
  font-size: 0.75rem;
}

/* Page d'exclusion des dates */
.exclusion-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
}

.exclusions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.exclusion-item {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition);
}

.exclusion-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--border-hover);
}

.exclusion-icon {
  font-size: 1.5rem;
  width: 40px;
  text-align: center;
}

.exclusion-content {
  flex: 1;
}

.exclusion-content h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.25rem 0;
}

.exclusion-date {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin: 0 0 0.5rem 0;
  text-transform: capitalize;
}

.type-badge.FERIE {
  background: var(--danger-light);
  color: var(--danger-color);
}

.type-badge.CONGE {
  background: var(--warning-light);
  color: var(--warning-color);
}

.type-badge.FORMATION {
  background: var(--primary-light);
  color: var(--primary-color);
}

.type-badge.AUTRE {
  background: var(--secondary-color);
  color: var(--text-white);
}

.exclusion-actions {
  display: flex;
  gap: 0.5rem;
}

/* Instructions */
.instructions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.step {
  background: var(--primary-color);
  color: var(--text-white);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 0.25rem 0;
}

.step-content p {
  color: var(--text-muted);
  font-size: 0.75rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .main-content {
    margin-left: 0;
  }

  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .header-nav {
    justify-content: center;
  }

  .app-header h1 {
    font-size: 1.5rem;
  }

  .app-main {
    padding: 1rem;
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .section-content {
    padding: 1.5rem;
  }

  .config-grid {
    grid-template-columns: 1fr;
  }

  .medecin-grid {
    max-height: 200px;
  }

  .medecin-table-container {
    max-height: 300px;
  }

  .garde-counter {
    gap: 0.5rem;
  }

  .btn-counter {
    min-width: 2.5rem;
    height: 2.5rem;
  }

  /* Planning page responsive */
  .planning-container {
    flex-direction: column;
  }

  .medecins-sidebar {
    width: 100%;
    max-height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .calendar-grid {
    grid-template-columns: 1fr;
  }

  .date-card {
    margin-bottom: 1rem;
  }
}
