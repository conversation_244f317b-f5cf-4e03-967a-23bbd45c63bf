import { useState } from 'react';
import { gardeService } from '../services/api';

const GardeTable = ({ gardes, mois, annee, onGardeDeleted }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleDeleteGarde = async (gardeId, date) => {
    if (!window.confirm(`Êtes-vous sûr de vouloir supprimer la garde du ${date} ?`)) {
      return;
    }

    try {
      setLoading(true);
      await gardeService.delete(gardeId);
      
      if (onGardeDeleted) {
        onGardeDeleted(gardeId);
      }
    } catch (err) {
      setError(err.response?.data?.error || 'Erreur lors de la suppression');
    } finally {
      setLoading(false);
    }
  };

  const handleExportWord = async () => {
    try {
      setLoading(true);
      const blob = await gardeService.exportToWord(mois, annee);
      
      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `gardes-${mois}-${annee}.docx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
    } catch (err) {
      setError(err.response?.data?.error || 'Erreur lors de l\'exportation');
    } finally {
      setLoading(false);
    }
  };

  if (!gardes || gardes.length === 0) {
    return null;
  }

  return (
    <div className="garde-table">
      <div className="table-header">
        <h2>🗓️ Planning des gardes - {mois.charAt(0).toUpperCase() + mois.slice(1)} {annee}</h2>
        
        <button
          onClick={handleExportWord}
          disabled={loading}
          className="btn btn-success"
        >
          {loading ? '⏳ Export...' : '📝 Exporter en Word'}
        </button>
      </div>

      {error && (
        <div className="error-message">
          ❌ {error}
        </div>
      )}

      <div className="table-container">
        <table className="garde-planning-table">
          <thead>
            <tr>
              <th>Date</th>
              <th>Jour</th>
              <th>Assistant</th>
              <th>Résidant</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {gardes.map((garde) => (
              <tr key={garde.id}>
                <td className="date-cell">{garde.date}</td>
                <td className="jour-cell">{garde.jour || ''}</td>
                <td className="medecin-cell">
                  {garde.medecinAssistant ? (
                    <span className="medecin-name">
                      👨‍⚕️ {garde.medecinAssistant.prenom} {garde.medecinAssistant.nom}
                    </span>
                  ) : (
                    <span className="no-medecin">-</span>
                  )}
                </td>
                <td className="medecin-cell">
                  {garde.medecinResidant ? (
                    <span className="medecin-name">
                      🎓 {garde.medecinResidant.prenom} {garde.medecinResidant.nom}
                    </span>
                  ) : (
                    <span className="no-medecin">-</span>
                  )}
                </td>
                <td className="actions-cell">
                  <button
                    onClick={() => handleDeleteGarde(garde.id, garde.date)}
                    disabled={loading}
                    className="btn btn-danger btn-small"
                    title="Supprimer cette garde (jour férié, etc.)"
                  >
                    🗑️
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="table-footer">
        <div className="stats">
          <span>Total des gardes : {gardes.length}</span>
          <span>
            Assistants : {gardes.filter(g => g.medecinAssistant).length}
          </span>
          <span>
            Résidants : {gardes.filter(g => g.medecinResidant).length}
          </span>
        </div>
      </div>
    </div>
  );
};

export default GardeTable;
