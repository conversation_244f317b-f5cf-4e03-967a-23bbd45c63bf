const express = require('express');
const { PrismaClient } = require('@prisma/client');

const router = express.Router();
const prisma = new PrismaClient();

// GET /api/medecins - Récupérer tous les médecins
router.get('/', async (req, res) => {
  try {
    const medecins = await prisma.medecin.findMany({
      orderBy: [
        { nom: 'asc' },
        { prenom: 'asc' }
      ]
    });
    res.json(medecins);
  } catch (error) {
    console.error('Erreur lors de la récupération des médecins:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// POST /api/medecins - Ajouter un médecin
router.post('/', async (req, res) => {
  try {
    const { nom, prenom, type } = req.body;

    // Validation
    if (!nom || !prenom || !type) {
      return res.status(400).json({ error: 'Nom, prénom et type sont requis' });
    }

    if (!['Residant', 'Assistant'].includes(type)) {
      return res.status(400).json({ error: 'Type doit être "Residant" ou "Assistant"' });
    }

    const medecin = await prisma.medecin.create({
      data: {
        nom: nom.trim(),
        prenom: prenom.trim(),
        type: type
      }
    });

    res.status(201).json(medecin);
  } catch (error) {
    console.error('Erreur lors de l\'ajout du médecin:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

// DELETE /api/medecins/:id - Supprimer un médecin
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const medecinId = parseInt(id);

    if (isNaN(medecinId)) {
      return res.status(400).json({ error: 'ID invalide' });
    }

    // Vérifier si le médecin existe
    const medecin = await prisma.medecin.findUnique({
      where: { id: medecinId }
    });

    if (!medecin) {
      return res.status(404).json({ error: 'Médecin non trouvé' });
    }

    // Supprimer le médecin (les relations seront gérées par Prisma)
    await prisma.medecin.delete({
      where: { id: medecinId }
    });

    res.json({ message: 'Médecin supprimé avec succès' });
  } catch (error) {
    console.error('Erreur lors de la suppression du médecin:', error);
    res.status(500).json({ error: 'Erreur serveur' });
  }
});

module.exports = router;
