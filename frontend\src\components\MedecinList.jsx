import { useState, useEffect } from 'react';
import { medecinService } from '../services/api';

const MedecinList = ({ refreshTrigger }) => {
  const [medecins, setMedecins] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const loadMedecins = async () => {
    try {
      setLoading(true);
      const data = await medecinService.getAll();
      setMedecins(data);
      setError('');
    } catch (err) {
      setError(err.response?.data?.error || 'Erreur lors du chargement');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id, nom, prenom) => {
    if (!window.confirm(`Êtes-vous sûr de vouloir supprimer ${prenom} ${nom} ?`)) {
      return;
    }

    try {
      await medecinService.delete(id);
      await loadMedecins(); // Recharger la liste
    } catch (err) {
      setError(err.response?.data?.error || 'Erreur lors de la suppression');
    }
  };

  useEffect(() => {
    loadMedecins();
  }, [refreshTrigger]);

  if (loading) {
    return <div className="loading">⏳ Chargement des médecins...</div>;
  }

  if (error) {
    return (
      <div className="error-container">
        <div className="error-message">❌ {error}</div>
        <button onClick={loadMedecins} className="btn btn-secondary">
          🔄 Réessayer
        </button>
      </div>
    );
  }

  return (
    <div className="medecin-list">
      <h2>👥 Liste des médecins ({medecins.length})</h2>
      
      {medecins.length === 0 ? (
        <div className="empty-state">
          <p>Aucun médecin enregistré</p>
          <p>Utilisez le formulaire ci-dessus pour ajouter des médecins.</p>
        </div>
      ) : (
        <div className="table-container">
          <table className="medecin-table">
            <thead>
              <tr>
                <th>Nom</th>
                <th>Prénom</th>
                <th>Type</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {medecins.map((medecin) => (
                <tr key={medecin.id}>
                  <td>{medecin.nom}</td>
                  <td>{medecin.prenom}</td>
                  <td>
                    <span className={`type-badge ${medecin.type.toLowerCase()}`}>
                      {medecin.type === 'Residant' ? '🎓 Résidant' : '👨‍⚕️ Assistant'}
                    </span>
                  </td>
                  <td>
                    <button
                      onClick={() => handleDelete(medecin.id, medecin.nom, medecin.prenom)}
                      className="btn btn-danger btn-small"
                      title="Supprimer ce médecin"
                    >
                      🗑️ Supprimer
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default MedecinList;
