{"name": "pg-types", "version": "4.0.2", "description": "Query result type converters for node-postgres", "main": "index.js", "scripts": {"coverage": "nyc --reporter=html npm test && open-cli coverage/index.html", "coverage-ci": "nyc --reporter=lcov npm test && codecov", "lint": "standard", "test": "tape test/*.js | tap-spec && npm run test-ts && npm run lint", "test-ts": "tsd"}, "repository": {"type": "git", "url": "git://github.com/brianc/node-pg-types.git"}, "keywords": ["postgres", "PostgreSQL", "pg"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/brianc/node-pg-types/issues"}, "homepage": "https://github.com/brianc/node-pg-types", "devDependencies": {"@types/node": "^14.14.33", "codecov": "^3.8.1", "nyc": "^15.1.0", "open-cli": "^6.0.1", "standard": "^16.0.3", "tap-spec": "^5.0.0", "tape": "^5.2.2", "tsd": "^0.14.0"}, "dependencies": {"pg-int8": "1.0.1", "pg-numeric": "1.0.2", "postgres-array": "~3.0.1", "postgres-bytea": "~3.0.0", "postgres-date": "~2.1.0", "postgres-interval": "^3.0.0", "postgres-range": "^1.1.1"}, "files": ["index.js", "index.d.ts", "lib"], "engines": {"node": ">=10"}}