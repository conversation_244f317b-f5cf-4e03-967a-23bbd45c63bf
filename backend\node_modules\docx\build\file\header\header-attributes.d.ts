import { XmlAttributeComponent } from '../xml-components';
export declare class HeaderAttributes extends XmlAttributeComponent<{
    readonly wpc?: string;
    readonly mc?: string;
    readonly o?: string;
    readonly r?: string;
    readonly m?: string;
    readonly v?: string;
    readonly wp14?: string;
    readonly wp?: string;
    readonly w10?: string;
    readonly w?: string;
    readonly w14?: string;
    readonly w15?: string;
    readonly wpg?: string;
    readonly wpi?: string;
    readonly wne?: string;
    readonly wps?: string;
    readonly cp?: string;
    readonly dc?: string;
    readonly dcterms?: string;
    readonly dcmitype?: string;
    readonly xsi?: string;
    readonly type?: string;
    readonly cx?: string;
    readonly cx1?: string;
    readonly cx2?: string;
    readonly cx3?: string;
    readonly cx4?: string;
    readonly cx5?: string;
    readonly cx6?: string;
    readonly cx7?: string;
    readonly cx8?: string;
    readonly w16cid: string;
    readonly w16se: string;
}> {
    protected readonly xmlKeys: {
        wpc: string;
        mc: string;
        o: string;
        r: string;
        m: string;
        v: string;
        wp14: string;
        wp: string;
        w10: string;
        w: string;
        w14: string;
        w15: string;
        wpg: string;
        wpi: string;
        wne: string;
        wps: string;
        cp: string;
        dc: string;
        dcterms: string;
        dcmitype: string;
        xsi: string;
        type: string;
        cx: string;
        cx1: string;
        cx2: string;
        cx3: string;
        cx4: string;
        cx5: string;
        cx6: string;
        cx7: string;
        cx8: string;
        w16cid: string;
        w16se: string;
    };
}
