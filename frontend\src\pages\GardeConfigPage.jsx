import GardeAttribution from '../components/GardeAttribution';

const GardeConfigPage = ({ onGardesGenerated }) => {
  return (
    <div className="garde-config-page">
      <div className="page-header">
        <div className="header-content">
          <h2>⚙️ Configuration des Gardes</h2>
          <p>Configurez et générez les plannings de gardes pour vos équipes</p>
        </div>
      </div>

      <div className="page-content">
        <div className="config-section">
          <div className="section-card">
            <div className="card-header">
              <h3>📅 Nouvelle configuration</h3>
              <p>Sélectionnez un mois et attribuez les quotas de gardes par médecin</p>
            </div>
            
            <div className="config-content">
              <GardeAttribution onGardesGenerated={onGardesGenerated} />
            </div>
          </div>
        </div>

        <div className="info-section">
          <div className="section-card info">
            <h3>ℹ️ Instructions</h3>
            <div className="instructions">
              <div className="instruction-item">
                <span className="step">1</span>
                <div className="step-content">
                  <h4>Sélectionnez le mois</h4>
                  <p>Choisissez le mois et l'année pour lesquels vous voulez créer un planning</p>
                </div>
              </div>
              
              <div className="instruction-item">
                <span className="step">2</span>
                <div className="step-content">
                  <h4>Attribuez les quotas</h4>
                  <p>Utilisez les boutons ➕ ➖ pour définir le nombre de gardes par médecin</p>
                </div>
              </div>
              
              <div className="instruction-item">
                <span className="step">3</span>
                <div className="step-content">
                  <h4>Générez le planning</h4>
                  <p>Cliquez sur "Générer" pour ouvrir l'interface de planification drag & drop</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GardeConfigPage;
