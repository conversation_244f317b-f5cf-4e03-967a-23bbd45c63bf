import { useState, useEffect } from 'react';
import { medecinService, gardeService } from '../services/api';

const GardePlanningPage = ({ mois, annee, typeJour, onClose }) => {
  const [medecins, setMedecins] = useState([]);
  const [dates, setDates] = useState([]);
  const [planning, setPlanning] = useState({});
  const [draggedMedecin, setDraggedMedecin] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const jours = ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'];
  const moisNames = [
    'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
    'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
  ];

  useEffect(() => {
    loadData();
  }, [mois, annee]);

  const loadData = async () => {
    try {
      setLoading(true);
      const medecinData = await medecinService.getAll();
      setMedecins(medecinData);

      // Générer les dates du mois selon le type
      const moisIndex = moisNames.indexOf(mois.toLowerCase());
      const nombreJours = new Date(annee, moisIndex + 1, 0).getDate();
      const datesArray = [];

      for (let jour = 1; jour <= nombreJours; jour++) {
        const date = new Date(annee, moisIndex, jour);
        const dayOfWeek = date.getDay(); // 0=dimanche, 1=lundi, ..., 6=samedi

        // Filtrer selon le type de jour
        let includeDay = false;
        if (typeJour === 'SEMAINE') {
          // Dimanche (0) à Jeudi (4)
          includeDay = dayOfWeek >= 0 && dayOfWeek <= 4;
        } else if (typeJour === 'WEEKEND') {
          // Vendredi (5) et Samedi (6)
          includeDay = dayOfWeek === 5 || dayOfWeek === 6;
        }

        if (includeDay) {
          datesArray.push({
            date: `${jour.toString().padStart(2, '0')}/${(moisIndex + 1).toString().padStart(2, '0')}/${annee}`,
            jour: jours[dayOfWeek],
            dayNumber: jour,
            typeJour: typeJour
          });
        }
      }

      setDates(datesArray);

      // Initialiser le planning vide
      const initialPlanning = {};
      datesArray.forEach(dateInfo => {
        initialPlanning[dateInfo.date] = {
          assistant: null,
          residant: null
        };
      });
      setPlanning(initialPlanning);

    } catch (err) {
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const handleDragStart = (e, medecin) => {
    setDraggedMedecin(medecin);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e, date, type) => {
    e.preventDefault();

    if (!draggedMedecin) return;

    // Vérifier que le type correspond
    if (draggedMedecin.type === 'Assistant' && type !== 'assistant') return;
    if (draggedMedecin.type === 'Residant' && type !== 'residant') return;

    setPlanning(prev => ({
      ...prev,
      [date]: {
        ...prev[date],
        [type]: draggedMedecin
      }
    }));

    setDraggedMedecin(null);
  };

  const removeMedecinFromDate = (date, type) => {
    setPlanning(prev => ({
      ...prev,
      [date]: {
        ...prev[date],
        [type]: null
      }
    }));
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      // Convertir le planning en format pour l'API
      const gardes = dates.map(dateInfo => ({
        date: dateInfo.date,
        jour: dateInfo.jour,
        medecinAssistantId: planning[dateInfo.date]?.assistant?.id || null,
        medecinResidantId: planning[dateInfo.date]?.residant?.id || null,
        mois: mois,
        annee: annee
      }));

      // Sauvegarder via l'API
      await gardeService.saveGardes(gardes);

      alert('Planning sauvegardé avec succès !');
      onClose();

    } catch (err) {
      setError('Erreur lors de la sauvegarde');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      setLoading(true);

      // D'abord sauvegarder le planning
      const gardes = dates.map(dateInfo => ({
        date: dateInfo.date,
        jour: dateInfo.jour,
        medecinAssistantId: planning[dateInfo.date]?.assistant?.id || null,
        medecinResidantId: planning[dateInfo.date]?.residant?.id || null,
        mois: mois,
        annee: annee
      }));

      await gardeService.saveGardes(gardes);

      // Puis exporter
      const blob = await gardeService.exportToWord(mois, annee);

      // Créer un lien de téléchargement
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `planning-gardes-${mois}-${annee}.docx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

    } catch (err) {
      setError('Erreur lors de l\'exportation');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="planning-page">
        <div className="loading-container">
          <div className="spinner"></div>
          <p>Chargement du planning...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="planning-page">
      <div className="planning-header">
        <div className="header-content">
          <h1>🗓️ Planning des Gardes - {mois.charAt(0).toUpperCase() + mois.slice(1)} {annee}</h1>
          <div className="header-actions">
            <button onClick={onClose} className="btn btn-secondary">
              ← Retour
            </button>
            <button onClick={handleSave} className="btn btn-primary" disabled={loading}>
              💾 Sauvegarder
            </button>
            <button onClick={handleExport} className="btn btn-success" disabled={loading}>
              📄 Exporter Word
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="error-message">
          ❌ {error}
        </div>
      )}

      <div className="planning-container">
        {/* Sidebar avec les médecins */}
        <div className="medecins-sidebar">
          <h3>👥 Médecins disponibles</h3>

          <div className="medecin-category">
            <h4>👨‍⚕️ Assistants</h4>
            <div className="medecin-list">
              {medecins.filter(m => m.type === 'Assistant').map(medecin => (
                <div
                  key={medecin.id}
                  className="medecin-card draggable"
                  draggable
                  onDragStart={(e) => handleDragStart(e, medecin)}
                >
                  <span className="medecin-name">
                    {medecin.prenom} {medecin.nom}
                  </span>
                  <span className="medecin-type assistant">Assistant</span>
                </div>
              ))}
            </div>
          </div>

          <div className="medecin-category">
            <h4>🎓 Résidants</h4>
            <div className="medecin-list">
              {medecins.filter(m => m.type === 'Residant').map(medecin => (
                <div
                  key={medecin.id}
                  className="medecin-card draggable"
                  draggable
                  onDragStart={(e) => handleDragStart(e, medecin)}
                >
                  <span className="medecin-name">
                    {medecin.prenom} {medecin.nom}
                  </span>
                  <span className="medecin-type residant">Résidant</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Calendrier */}
        <div className="calendar-container">
          <div className="calendar-grid">
            {dates.map(dateInfo => (
              <div key={dateInfo.date} className="date-card">
                <div className="date-header">
                  <span className="date-number">{dateInfo.dayNumber}</span>
                  <span className="date-day">{dateInfo.jour}</span>
                </div>

                <div className="garde-slots">
                  {/* Slot Assistant */}
                  <div
                    className="garde-slot assistant-slot"
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, dateInfo.date, 'assistant')}
                  >
                    <div className="slot-label">👨‍⚕️ Assistant</div>
                    {planning[dateInfo.date]?.assistant ? (
                      <div className="assigned-medecin">
                        <span>{planning[dateInfo.date].assistant.prenom} {planning[dateInfo.date].assistant.nom}</span>
                        <button
                          onClick={() => removeMedecinFromDate(dateInfo.date, 'assistant')}
                          className="remove-btn"
                        >
                          ✕
                        </button>
                      </div>
                    ) : (
                      <div className="empty-slot">Glisser un assistant ici</div>
                    )}
                  </div>

                  {/* Slot Résidant */}
                  <div
                    className="garde-slot residant-slot"
                    onDragOver={handleDragOver}
                    onDrop={(e) => handleDrop(e, dateInfo.date, 'residant')}
                  >
                    <div className="slot-label">🎓 Résidant</div>
                    {planning[dateInfo.date]?.residant ? (
                      <div className="assigned-medecin">
                        <span>{planning[dateInfo.date].residant.prenom} {planning[dateInfo.date].residant.nom}</span>
                        <button
                          onClick={() => removeMedecinFromDate(dateInfo.date, 'residant')}
                          className="remove-btn"
                        >
                          ✕
                        </button>
                      </div>
                    ) : (
                      <div className="empty-slot">Glisser un résidant ici</div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GardePlanningPage;
