// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Medecin {
  id        Int         @id @default(autoincrement())
  nom       String
  prenom    String
  type      TypeMedecin
  createdAt DateTime    @default(now()) @map("created_at")

  // Relations
  gardesAssistant          GardeConfig[] @relation("MedecinConfig")
  gardesEnTantQueAssistant Garde[]       @relation("GardeAssistant")
  gardesEnTantQueResidant  Garde[]       @relation("GardeResidant")

  @@map("medecins")
}

model GardeConfig {
  id           Int    @id @default(autoincrement())
  medecinId    Int    @map("medecin_id")
  nombreGardes Int    @map("nombre_gardes")
  mois         String
  annee        Int

  // Relations
  medecin Medecin @relation("MedecinConfig", fields: [medecinId], references: [id], onDelete: Cascade)

  @@unique([medecinId, mois, annee])
  @@map("garde_config")
}

model Garde {
  id                 Int      @id @default(autoincrement())
  date               String
  medecinAssistantId Int?     @map("medecin_assistant_id")
  medecinResidantId  Int?     @map("medecin_residant_id")
  mois               String
  annee              Int
  createdAt          DateTime @default(now()) @map("created_at")

  // Relations
  medecinAssistant Medecin? @relation("GardeAssistant", fields: [medecinAssistantId], references: [id], onDelete: SetNull)
  medecinResidant  Medecin? @relation("GardeResidant", fields: [medecinResidantId], references: [id], onDelete: SetNull)

  @@map("gardes")
}

enum TypeMedecin {
  Residant
  Assistant
}
