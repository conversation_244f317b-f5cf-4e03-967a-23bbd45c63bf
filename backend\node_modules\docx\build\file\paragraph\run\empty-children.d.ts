import { EmptyElement } from '../../xml-components';
export declare class NoBreakHyphen extends EmptyElement {
    constructor();
}
export declare class SoftHyphen extends EmptyElement {
    constructor();
}
export declare class DayShort extends EmptyElement {
    constructor();
}
export declare class MonthShort extends EmptyElement {
    constructor();
}
export declare class YearShort extends EmptyElement {
    constructor();
}
export declare class DayLong extends EmptyElement {
    constructor();
}
export declare class MonthLong extends EmptyElement {
    constructor();
}
export declare class YearLong extends EmptyElement {
    constructor();
}
export declare class AnnotationReference extends EmptyElement {
    constructor();
}
export declare class FootnoteReferenceElement extends EmptyElement {
    constructor();
}
export declare class EndnoteReference extends EmptyElement {
    constructor();
}
export declare class Separator extends EmptyElement {
    constructor();
}
export declare class ContinuationSeparator extends EmptyElement {
    constructor();
}
export declare class PageNumberElement extends EmptyElement {
    constructor();
}
export declare class CarriageReturn extends EmptyElement {
    constructor();
}
export declare class Tab extends EmptyElement {
    constructor();
}
export declare class LastRenderedPageBreak extends EmptyElement {
    constructor();
}
